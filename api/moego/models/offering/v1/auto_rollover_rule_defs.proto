// @since 2024-06-03 22:12:35
// <AUTHOR> <zhang<PERSON>@moego.pet>

syntax = "proto3";

package moego.models.offering.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// service auto rollover rule
message AutoRolloverRuleDef {
  // auto rollover enabled
  bool enabled = 1;
  // auto rollover after x minutes, only valid when enabled is true
  optional int32 after_minute = 2 [(validate.rules).int32 = {
    gte: 0
    lte: 60
  }];
  // auto rollover target service id, only valid when enabled is true
  optional int64 target_service_id = 3 [(validate.rules).int64 = {gte: 0}];
}
