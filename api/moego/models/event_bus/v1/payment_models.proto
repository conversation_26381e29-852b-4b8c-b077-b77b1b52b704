syntax = "proto3";

package moego.models.event_bus.v1;

import "moego/models/payment/v1/dispute_models.proto";
import "moego/models/payment/v2/payment_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1;eventbuspb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.event_bus.v1";

// PaymentEvent
message PaymentEvent {
  // payment
  models.payment.v2.PaymentModel payment = 1;
}

// PaymentRefundEvent
message PaymentRefundEvent {
  // refund
  models.payment.v2.RefundModel refund = 1;
}

// Dispute funding operate event
message DisputeFundingOperateEvent {
  // dispute
  models.payment.v1.DisputeFundingOperateModel dispute_funding_operate = 1;
}
