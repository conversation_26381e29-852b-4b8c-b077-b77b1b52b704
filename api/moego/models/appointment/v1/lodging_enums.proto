// @since 2024-01-15 16:36:40
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// lodging status
enum LodgingStatus {
  // unspecified value
  LODGING_STATUS_UNSPECIFIED = 0;
  // all status
  LODGING_STATUS_ALL = 1;
  // vacant status
  LODGING_STATUS_VACANT = 2;
  // occupied status
  LODGING_STATUS_OCCUPIED = 3;
}

// lodging status
enum LodgingOccupiedStatus {
  // unspecified value
  LODGING_OCCUPIED_STATUS_UNSPECIFIED = 0;
  // vacant status
  VACANT = 1;
  // partially occupied status
  PARTIALLY_OCCUPIED = 2;
  // fully occupied status
  FULLY_OCCUPIED = 3;
}
