syntax = "proto3";

package moego.models.account.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/account/v1;accountpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.account.v1";

// oauth account provider
enum OauthAccountProvider {
  // unspecified
  OAUTH_ACCOUNT_PROVIDER_UNSPECIFIED = 0;
  // google
  OAUTH_ACCOUNT_PROVIDER_GOOGLE = 1;
  // apple
  OAUTH_ACCOUNT_PROVIDER_APPLE = 2;
  // facebook
  OAUTH_ACCOUNT_PROVIDER_FACEBOOK = 3;
}
