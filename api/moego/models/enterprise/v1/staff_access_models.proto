syntax = "proto3";

package moego.models.enterprise.v1;

import "moego/models/enterprise/v1/tenant_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// staff access relation
message StaffAccess {
  // id
  int64 id = 1;
  // staff id
  int64 staff_id = 2;
  // resource
  TenantObject resource = 3;
}
