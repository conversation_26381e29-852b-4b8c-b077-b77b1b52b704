// @since 2025-03-05 10:27:11
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v2;

import "moego/models/appointment/v2/pricing_rule_apply_enums.proto";
import "moego/models/appointment/v2/pricing_rule_apply_models.proto";
import "moego/models/offering/v2/pricing_rule_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v2;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v2";

// list pricing_rule_apply request
message ListPricingRuleApplyLogRequest {
  // source id
  int64 source_id = 4 [(validate.rules).int64 = {gt: 0}];
  // source type
  moego.models.appointment.v2.PricingRuleApplySourceType source_type = 5 [(validate.rules).enum = {defined_only: true}];
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// list pricing_rule_apply response
message ListPricingRuleApplyLogResponse {
  // the pricing_rule_apply
  repeated moego.models.appointment.v2.PricingRuleApplyLogModel pricing_rule_apply_logs = 1;
}

// Update upcoming appointment pet details request
message UpdateUpcomingAppointmentUsingPricingRuleRequest {
  // associated service ids
  // Appointments with associated service IDs will be updated
  repeated int64 service_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];

  // staff id
  // The staff id of the staff who updated the service
  int64 staff_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// Update upcoming appt pet details response
message UpdateUpcomingAppointmentUsingPricingRuleResponse {
  // affected appointment count
  int64 affected_appointment_count = 1;
}

// apply pricing rule request
message ApplyPricingRuleRequest {
  // company id
  int64 company_id = 2 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];

  // source id
  int64 source_id = 4 [(validate.rules).int64 = {gt: 0}];
  // source type
  moego.models.appointment.v2.PricingRuleApplySourceType source_type = 5 [(validate.rules).enum = {defined_only: true}];
  // pet detail list
  repeated moego.models.offering.v2.PetDetailCalculateDef pet_details = 6;
}

// apply pricing rule response
message ApplyPricingRuleResponse {
  // pet detail list
  repeated moego.models.offering.v2.PetDetailCalculateResultDef pet_details = 1;
}

// the pricing_rule_apply service
service PricingRuleApplyService {
  // list pricing_rule_apply
  rpc ListPricingRuleApplyLog(ListPricingRuleApplyLogRequest) returns (ListPricingRuleApplyLogResponse);

  // Update upcoming appointment pet details
  rpc UpdateUpcomingAppointmentUsingPricingRule(UpdateUpcomingAppointmentUsingPricingRuleRequest) returns (UpdateUpcomingAppointmentUsingPricingRuleResponse);

  // apply pricing rule
  rpc ApplyPricingRule(ApplyPricingRuleRequest) returns (ApplyPricingRuleResponse);
}
