syntax = "proto3";

package moego.service.enterprise.v1;

import "moego/models/enterprise/v1/price_book_models.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1;enterprisesvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.enterprise.v1";

// create lodging type
message CreateLodgingTypeRequest {
  // enterprise id
  int64 enterprise_id = 1 [(validate.rules).int64.gt = 0];
  // lodging
  moego.models.enterprise.v1.CreateLodgingTypeDef lodging_type_def = 2;
}

/**
 * Response body for create LodgingType
 */
message CreateLodgingTypeResponse {
  // lodging type
  models.enterprise.v1.LodgingType lodging_type = 1;
}

/**
 * Request body for update LodgingType
 */
message UpdateLodgingTypeRequest {
  // id of the lodging type
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // update lodging type
  moego.models.enterprise.v1.UpdateLodgingTypeDef lodging_type_def = 2;
}

/**
 * Response body for update LodgingType
 */
message UpdateLodgingTypeResponse {
  // lodging type
  models.enterprise.v1.LodgingType lodging_type = 1;
}

/**
 * Request body for delete LodgingType
 */
message DeleteLodgingTypeRequest {
  // id of the lodging type
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
}

/**
 * Response body for delete LodgingType
 */
message DeleteLodgingTypeResponse {}

// The params for sort lodging types
message SortLodgingTypesRequest {
  // ids of lodging type to sort
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// The result for sort lodging types
message SortLodgingTypesResponse {}

// list lodging types request
message ListLodgingTypesRequest {
  // filter
  message Filter {
    // lodging type ids
    repeated int64 ids = 1;
    // enterprise ids
    repeated int64 enterprise_ids = 2;
  }
  // filter
  Filter filter = 1;
}

// list lodging types response
message ListLodgingTypesResponse {
  // lodging types
  repeated models.enterprise.v1.LodgingType lodging_types = 1;
}

// PushLodgingTypesRequest
message PushLodgingTypesRequest {
  // enterprise id
  int64 enterprise_id = 1;
  // ids
  repeated int64 lodging_type_ids = 2;
  // targets
  repeated moego.models.enterprise.v1.TenantObject targets = 3;
  // pushed by staff id
  optional int64 pushed_by_staff_id = 4;
}

// PushLodgingTypesResponse
message PushLodgingTypesResponse {
  // success company ids
  repeated int64 success_company_ids = 1;
  // failed company ids
  repeated int64 failed_company_ids = 2;
}

// init lodging types request
message InitLodgingTypesRequest {
  // enterprise ids
  repeated int64 enterprise_ids = 1;
}

// init lodging types response
message InitLodgingTypesResponse {}
