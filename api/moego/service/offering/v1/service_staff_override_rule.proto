syntax = "proto3";

package moego.service.offering.v1;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// ListServiceStaffOverrideRule request
message ListServiceStaffOverrideRuleRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service ids
  repeated int64 service_ids = 3 [(validate.rules).repeated = {
    max_items: 2000
    items: {
      int64: {gt: 0}
    }
  }];
}

// ListServiceStaffOverrideRule response
message ListServiceStaffOverrideRuleResponse {
  // staff override rule list
  repeated ServiceStaffOverrideRule rules = 1;

  // service staff override rule
  message ServiceStaffOverrideRule {
    // service id
    int64 service_id = 1;
    // staff id
    int64 staff_id = 2;
    // price
    optional double price = 3;
    // duration
    optional int32 duration = 4;
  }
}

// ServiceStaffOverrideRule service
service ServiceStaffOverrideRuleService {
  // List ServiceStaffOverrideRule
  rpc ListServiceStaffOverrideRule(ListServiceStaffOverrideRuleRequest) returns (ListServiceStaffOverrideRuleResponse);
}
