syntax = "proto3";

package moego.client.notification.v1;

import "moego/models/notification/v1/notification_enums.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/notification/v1;notificationapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.notification.v1";

// Refresh device params
message RefreshDeviceParams {
  // push token
  string push_token = 1 [(validate.rules).string = {max_len: 255}];
  // device type
  models.notification.v1.DeviceType device_type = 2 [(validate.rules).enum = {defined_only: true}];
}

// Refresh device result
message RefreshDeviceResult {
  // is online, true if the device is registered successfully, false if the device is offline successfully
  bool is_online = 1;
}

// the app push service
service AppPushService {
  // Register device, there is a session registration refresh device, but there is no session offline device.
  rpc RefreshDevice(RefreshDeviceParams) returns (RefreshDeviceResult);
}
