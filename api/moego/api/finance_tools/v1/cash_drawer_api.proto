syntax = "proto3";

package moego.api.finance_tools.v1;

import "google/type/interval.proto";
import "google/type/money.proto";
import "moego/models/finance_tools/v1/cash_drawer_defs.proto";
import "moego/models/finance_tools/v1/cash_drawer_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/finance_tools/v1;financetoolsapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.finance_tools.v1";

// Request for ListReports
message ListReportsRequest {
  // Pagination
  moego.utils.v2.PaginationRequest pagination = 1;
}

// Response for ListReports
message ListReportsResponse {
  // Pagination
  moego.utils.v2.PaginationResponse pagination = 1;
  // Reports
  repeated moego.models.finance_tools.v1.CashDrawerReport reports = 2;
}

// Request for GetLastReport
message GetLastReportRequest {}

// Response for GetLastReport
message GetLastReportResponse {
  // The last report
  optional moego.models.finance_tools.v1.CashDrawerReport last_report = 1;
}

// Request for GetReportedCashTotal
message GetReportedCashTotalRequest {
  // The range
  google.type.Interval range = 1 [(validate.rules).message.required = true];
}

// Response for GetReportedCashTotal
message GetReportedCashTotalResponse {
  // The reported total amount
  google.type.Money reported_cash_total = 1;
}

// Request for ListCashAdjustments
message ListCashAdjustmentsRequest {
  // Pagination
  moego.utils.v2.PaginationRequest pagination = 1;
  // Result sorting. The result is always sorted by the "created_at" field. Set to true if you want to sort by ascending
  // order. The default is false (descending order).
  optional bool asc = 2;
  // The range. If not provided, all adjustments under current business are returned.
  optional google.type.Interval range = 3;
  // The report ID. If specified, only adjustments linked to the report are returned.
  optional int64 report_id = 4 [(validate.rules).int64.gt = 0];
}

// Response for ListCashAdjustments
message ListCashAdjustmentsResponse {
  // Pagination
  moego.utils.v2.PaginationResponse pagination = 1;
  // Transactions
  repeated moego.models.finance_tools.v1.CashDrawerAdjustment adjustments = 2;
}

// Request for CreateCashAdjustment
message CreateCashAdjustmentRequest {
  // Adjustment
  moego.models.finance_tools.v1.CreateCashDrawerAdjustmentDef adjustment = 1 [(validate.rules).message.required = true];
}

// Response for CreateCashAdjustment
message CreateCashAdjustmentResponse {
  // The created adjustment
  moego.models.finance_tools.v1.CashDrawerAdjustment adjustment = 1;
}

// Request for CreateReport
message CreateReportRequest {
  // Report
  moego.models.finance_tools.v1.CreateCashDrawerReportDef report = 1 [(validate.rules).message.required = true];
}

// Response for CreateReport
message CreateReportResponse {
  // The created report
  moego.models.finance_tools.v1.CashDrawerReport report = 1;
}

// Request for UpdateReport
message UpdateReportRequest {
  // Report
  moego.models.finance_tools.v1.UpdateCashDrawerReportDef report = 1 [(validate.rules).message.required = true];
}

// Response for UpdateReport
message UpdateReportResponse {}

// Cash drawer service.
// If not specified, all methods manipulates data over business.
service CashDrawerService {
  // List cash drawer reports.
  rpc ListReports(ListReportsRequest) returns (ListReportsResponse);
  // Get the last report on given timestamp.
  rpc GetLastReport(GetLastReportRequest) returns (GetLastReportResponse);
  // Get the reported cash total, summing the cash payments charged from the clients (through appointments, retail,
  // etc.) as well as the refunds.
  rpc GetReportedCashTotal(GetReportedCashTotalRequest) returns (GetReportedCashTotalResponse);
  // List cash adjustments.
  rpc ListCashAdjustments(ListCashAdjustmentsRequest) returns (ListCashAdjustmentsResponse);
  // Create a new cash in/out adjustment.
  rpc CreateCashAdjustment(CreateCashAdjustmentRequest) returns (CreateCashAdjustmentResponse);
  // Create a new report. Note that the un-attached cash adjustments within this report's range will also be attached to
  // this report.
  rpc CreateReport(CreateReportRequest) returns (CreateReportResponse);
  // Update a report.
  rpc UpdateReport(UpdateReportRequest) returns (UpdateReportResponse);
}
