// @since 2024-07-10 14:37:32
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.branded_app.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/branded_app/v1;brandedAppApiV1";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.branded_app.v1";

// The params for GetBrandedAppConfig
message GetBrandedAppConfigParams {}

// The result for GetBrandedAppConfig
message GetBrandedAppConfigResult {
  // the branded_app config
  BrandedAppConfigView config = 1;
}

// Branded app config view
message BrandedAppConfigView {
  // branded app id
  string branded_app_id = 1;
  // App icon url
  string app_icon_url = 2;
  // app name
  string app_name = 3;
}

// the branded_app service
service BrandedAppService {
  // get branded_app
  rpc GetBrandedAppConfig(GetBrandedAppConfigParams) returns (GetBrandedAppConfigResult);
}
