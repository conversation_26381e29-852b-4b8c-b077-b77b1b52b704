package com.moego.common.security;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @Description: 用于加密解密
 * @date 2020/9/2117:31
 * @Company blur
 */
public class Des3Util {
    private static final Logger log = LoggerFactory.getLogger(Des3Util.class);

    /**
     * 定义 加密算法,可用
     */
    private static final String ALGORITHM = "DESede";

    /** DES,DESede,Blowfish
     * keybyte为加密密钥，长度为24字节
     * src为被加密的数据缓冲区（源）
     */
    public static String encode(String key, String src) {
        try {
            // 生成密钥
            SecretKey deskey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            // 加密
            Cipher c1 = Cipher.getInstance(ALGORITHM);
            c1.init(Cipher.ENCRYPT_MODE, deskey);
            byte[] bytes = c1.doFinal(src.getBytes(StandardCharsets.UTF_8));
            return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
        } catch (Exception e) {
            log.error("加密出错", e);
        }
        return null;
    }

    /**
     *   key 为加密密钥，长度为24字节
     *   src为加密后的缓冲区
     */
    public static String decode(String key, String src) {
        try {
            byte[] decode = Base64.getUrlDecoder().decode(src);
            // 生成密钥
            SecretKey deskey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            // 解密
            Cipher c1 = Cipher.getInstance(ALGORITHM);
            c1.init(Cipher.DECRYPT_MODE, deskey);
            byte[] bytes = c1.doFinal(decode);
            return new String(bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("解密出错", e);
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
        // 添加新安全算法,如果用JCE就要把它添加进去
        // Security.addProvider(new com.sun.crypto.provider.SunJCE());
        String key = "AAAA33*2020_123456789AAA"; // 必须24位
        for (int i = 1; i < 100000; i++) {
            String data = encode(key, i + ""); // 加密
            System.out.println("加密后的字符串:" + data);

            if (data.contains("/") || data.contains("=") || data.contains("+")) {
                System.out.println(i);
                break;
            }
            data = decode(key, data); // 解密
            System.out.println("解密后的字符串:" + data);
        }
    }
}
