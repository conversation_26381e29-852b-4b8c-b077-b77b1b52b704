package com.moego.common.enums;

import java.math.BigDecimal;

public interface A2pConst {
    /**
     * company a2p 状态
     */
    String A2P_STATUS_NO = "No Required";

    String A2P_STATUS_ACTION = "Action Required";
    String A2P_STATUS_PENDING = "Pending";
    String A2P_STATUS_FAILED = "Failed";
    String A2P_STATUS_VERIFIED = "Verified";
    /**
     * a2p fee 当前状态 0 需要支付 1支付失败 2支付成功
     */
    Byte FEE_STATUS_NEED = 0;

    Byte FEE_STATUS_FAILED = 1;
    Byte FEE_STATUS_PAID = 2;
    BigDecimal A2P_CHARGE_FEE = BigDecimal.valueOf(6);
    BigDecimal A2P_CHARGE_FEE_20 = BigDecimal.valueOf(20);
    BigDecimal A2P_CHARGE_FEE_15 = BigDecimal.valueOf(15);
    // 、newRequired
    String AFS_OLD_FAIL_USER = "oldFailUser";
    String AFS_NEW_REQUIRED = "newRequired";
    String A2P_CHARGE_FEE_NAME = "A2P 10DLC Registration";
}
