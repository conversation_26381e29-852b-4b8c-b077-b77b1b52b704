package com.moego.common.enums;

/**
 * <AUTHOR> v1
 * <AUTHOR> v2
 */
public interface AgreementEnum {
    /**
     *     1 first time booking
     *     2 every booking
     *     3 not required
     */
    Byte AGREEMENT_REQUIRED_TYPE_FIRST = 1;

    Byte AGREEMENT_REQUIRED_TYPE_EVERY = 2;
    Byte AGREEMENT_REQUIRED_TYPE_NOT = 3;
    Byte AGREEMENT_CONFIRM = 1;
    Byte AGREEMENT_IS_REQUIRED_CONFIRM = 1;
    /**
     * 1 customer signature 2 business upload image
     */
    Byte AGREEMENT_TYPE_CUSTOMER = 1;

    Byte AGREEMENT_TYPE_BUSINESS = 2;
    /**
     * 1-url  2-mobile app 3-book online  4-intake form
     * @see ServiceEnum#SVC_TYPE_GROOMING
     * @see ServiceEnum#SVC_TYPE_BOARDING
     */
    Byte AGREEMENT_SOURCE_URL = 1;

    Byte AGREEMENT_SOURCE_MOBILE = 2;
    Byte AGREEMENT_SOURCE_OB = 3;
    Byte AGREEMENT_SOURCE_INTAKE_FORM = 4;

    Integer AGREEMENT_SIGN_TRUE = 1;
    Integer AGREEMENT_SIGN_FALSE = 0;
    Integer AGREEMENT_RECORD_STATUS_NORMAL = 0;
    Integer AGREEMENT_RECORD_STATUS_DELETE = 1;
}
