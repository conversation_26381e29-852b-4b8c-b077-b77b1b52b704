// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/feeding_models.proto

package onlinebookingpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Stores information about feedings.
type FeedingModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The primary key identifier for each feeding.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The booking request identifier.
	BookingRequestId int64 `protobuf:"varint,2,opt,name=booking_request_id,json=bookingRequestId,proto3" json:"booking_request_id,omitempty"`
	// The service detail identifier.
	ServiceDetailId int64 `protobuf:"varint,3,opt,name=service_detail_id,json=serviceDetailId,proto3" json:"service_detail_id,omitempty"`
	// service detail type, 2: boarding, 3: daycare
	ServiceDetailType v1.ServiceItemType `protobuf:"varint,4,opt,name=service_detail_type,json=serviceDetailType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_detail_type,omitempty"`
	// Feeding time.
	Time []*FeedingModel_FeedingSchedule `protobuf:"bytes,5,rep,name=time,proto3" json:"time,omitempty"`
	// Feeding amount, must be greater than 0.
	Amount float64 `protobuf:"fixed64,6,opt,name=amount,proto3" json:"amount,omitempty"`
	// Feeding unit.
	Unit string `protobuf:"bytes,7,opt,name=unit,proto3" json:"unit,omitempty"`
	// Food type.
	FoodType string `protobuf:"bytes,8,opt,name=food_type,json=foodType,proto3" json:"food_type,omitempty"`
	// Food source.
	FoodSource string `protobuf:"bytes,9,opt,name=food_source,json=foodSource,proto3" json:"food_source,omitempty"`
	// Feeding instructions.
	Instruction string `protobuf:"bytes,10,opt,name=instruction,proto3" json:"instruction,omitempty"`
	// createdAt
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// updatedAt
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Feeding note
	Note string `protobuf:"bytes,13,opt,name=note,proto3" json:"note,omitempty"`
	// Feeding amount, such as 1.2, 1/2, 1 etc.
	AmountStr *string `protobuf:"bytes,14,opt,name=amount_str,json=amountStr,proto3,oneof" json:"amount_str,omitempty"`
}

func (x *FeedingModel) Reset() {
	*x = FeedingModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_feeding_models_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedingModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedingModel) ProtoMessage() {}

func (x *FeedingModel) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_feeding_models_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedingModel.ProtoReflect.Descriptor instead.
func (*FeedingModel) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_feeding_models_proto_rawDescGZIP(), []int{0}
}

func (x *FeedingModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FeedingModel) GetBookingRequestId() int64 {
	if x != nil {
		return x.BookingRequestId
	}
	return 0
}

func (x *FeedingModel) GetServiceDetailId() int64 {
	if x != nil {
		return x.ServiceDetailId
	}
	return 0
}

func (x *FeedingModel) GetServiceDetailType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceDetailType
	}
	return v1.ServiceItemType(0)
}

func (x *FeedingModel) GetTime() []*FeedingModel_FeedingSchedule {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *FeedingModel) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *FeedingModel) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *FeedingModel) GetFoodType() string {
	if x != nil {
		return x.FoodType
	}
	return ""
}

func (x *FeedingModel) GetFoodSource() string {
	if x != nil {
		return x.FoodSource
	}
	return ""
}

func (x *FeedingModel) GetInstruction() string {
	if x != nil {
		return x.Instruction
	}
	return ""
}

func (x *FeedingModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FeedingModel) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *FeedingModel) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

func (x *FeedingModel) GetAmountStr() string {
	if x != nil && x.AmountStr != nil {
		return *x.AmountStr
	}
	return ""
}

// Feeding schedule.
type FeedingModel_FeedingSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Label for the schedule.
	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	// Time for the schedule, in minutes.
	Time int32 `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"`
}

func (x *FeedingModel_FeedingSchedule) Reset() {
	*x = FeedingModel_FeedingSchedule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_feeding_models_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedingModel_FeedingSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedingModel_FeedingSchedule) ProtoMessage() {}

func (x *FeedingModel_FeedingSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_feeding_models_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedingModel_FeedingSchedule.ProtoReflect.Descriptor instead.
func (*FeedingModel_FeedingSchedule) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_feeding_models_proto_rawDescGZIP(), []int{0, 0}
}

func (x *FeedingModel_FeedingSchedule) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *FeedingModel_FeedingSchedule) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

var File_moego_models_online_booking_v1_feeding_models_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_feeding_models_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x66, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xab, 0x05, 0x0a, 0x0c, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x10, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x59,
	0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x04, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x6f, 0x6f, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x6f, 0x6f, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x6f, 0x6f, 0x64, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x74,
	0x65, 0x12, 0x22, 0x0a, 0x0a, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53,
	0x74, 0x72, 0x88, 0x01, 0x01, 0x1a, 0x3b, 0x0a, 0x0f, 0x46, 0x65, 0x65, 0x64, 0x69, 0x6e, 0x67,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x69,
	0x6d, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x72, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_feeding_models_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_feeding_models_proto_rawDescData = file_moego_models_online_booking_v1_feeding_models_proto_rawDesc
)

func file_moego_models_online_booking_v1_feeding_models_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_feeding_models_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_feeding_models_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_feeding_models_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_feeding_models_proto_rawDescData
}

var file_moego_models_online_booking_v1_feeding_models_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_moego_models_online_booking_v1_feeding_models_proto_goTypes = []interface{}{
	(*FeedingModel)(nil),                 // 0: moego.models.online_booking.v1.FeedingModel
	(*FeedingModel_FeedingSchedule)(nil), // 1: moego.models.online_booking.v1.FeedingModel.FeedingSchedule
	(v1.ServiceItemType)(0),              // 2: moego.models.offering.v1.ServiceItemType
	(*timestamppb.Timestamp)(nil),        // 3: google.protobuf.Timestamp
}
var file_moego_models_online_booking_v1_feeding_models_proto_depIdxs = []int32{
	2, // 0: moego.models.online_booking.v1.FeedingModel.service_detail_type:type_name -> moego.models.offering.v1.ServiceItemType
	1, // 1: moego.models.online_booking.v1.FeedingModel.time:type_name -> moego.models.online_booking.v1.FeedingModel.FeedingSchedule
	3, // 2: moego.models.online_booking.v1.FeedingModel.created_at:type_name -> google.protobuf.Timestamp
	3, // 3: moego.models.online_booking.v1.FeedingModel.updated_at:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_feeding_models_proto_init() }
func file_moego_models_online_booking_v1_feeding_models_proto_init() {
	if File_moego_models_online_booking_v1_feeding_models_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_feeding_models_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedingModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_feeding_models_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedingModel_FeedingSchedule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_online_booking_v1_feeding_models_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_feeding_models_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_feeding_models_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_feeding_models_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_feeding_models_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_feeding_models_proto = out.File
	file_moego_models_online_booking_v1_feeding_models_proto_rawDesc = nil
	file_moego_models_online_booking_v1_feeding_models_proto_goTypes = nil
	file_moego_models_online_booking_v1_feeding_models_proto_depIdxs = nil
}
