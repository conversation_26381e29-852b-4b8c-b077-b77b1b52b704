// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/payment/v1/payment_enums.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// PaymentStatus is the status of a payment
type PaymentStatus int32

const (
	// created
	PaymentStatus_CREATED PaymentStatus = 0
	// processing
	PaymentStatus_PROCESSING PaymentStatus = 1
	// paid
	PaymentStatus_PAID PaymentStatus = 2
	// completed
	PaymentStatus_COMPLETED PaymentStatus = 3
	// failed
	PaymentStatus_FAILED PaymentStatus = -1
)

// Enum value maps for PaymentStatus.
var (
	PaymentStatus_name = map[int32]string{
		0:  "CREATED",
		1:  "PROCESSING",
		2:  "PAID",
		3:  "COMPLETED",
		-1: "FAILED",
	}
	PaymentStatus_value = map[string]int32{
		"CREATED":    0,
		"PROCESSING": 1,
		"PAID":       2,
		"COMPLETED":  3,
		"FAILED":     -1,
	}
)

func (x PaymentStatus) Enum() *PaymentStatus {
	p := new(PaymentStatus)
	*p = x
	return p
}

func (x PaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v1_payment_enums_proto_enumTypes[0].Descriptor()
}

func (PaymentStatus) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v1_payment_enums_proto_enumTypes[0]
}

func (x PaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentStatus.Descriptor instead.
func (PaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_enums_proto_rawDescGZIP(), []int{0}
}

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// Refund payment status.
type RefundPaymentStatus int32

const (
	// created
	RefundPaymentStatus_REFUND_PAYMENT_STATUS_CREATED RefundPaymentStatus = 0
	// processing
	RefundPaymentStatus_REFUND_PAYMENT_STATUS_PROCESSING RefundPaymentStatus = 1
	// paid
	RefundPaymentStatus_REFUND_PAYMENT_STATUS_PAID RefundPaymentStatus = 2
	// completed
	RefundPaymentStatus_REFUND_PAYMENT_STATUS_COMPLETED RefundPaymentStatus = 3
	// failed
	RefundPaymentStatus_REFUND_PAYMENT_STATUS_FAILED RefundPaymentStatus = -1
	// init
	RefundPaymentStatus_REFUND_PAYMENT_STATUS_INIT RefundPaymentStatus = -2
)

// Enum value maps for RefundPaymentStatus.
var (
	RefundPaymentStatus_name = map[int32]string{
		0:  "REFUND_PAYMENT_STATUS_CREATED",
		1:  "REFUND_PAYMENT_STATUS_PROCESSING",
		2:  "REFUND_PAYMENT_STATUS_PAID",
		3:  "REFUND_PAYMENT_STATUS_COMPLETED",
		-1: "REFUND_PAYMENT_STATUS_FAILED",
		-2: "REFUND_PAYMENT_STATUS_INIT",
	}
	RefundPaymentStatus_value = map[string]int32{
		"REFUND_PAYMENT_STATUS_CREATED":    0,
		"REFUND_PAYMENT_STATUS_PROCESSING": 1,
		"REFUND_PAYMENT_STATUS_PAID":       2,
		"REFUND_PAYMENT_STATUS_COMPLETED":  3,
		"REFUND_PAYMENT_STATUS_FAILED":     -1,
		"REFUND_PAYMENT_STATUS_INIT":       -2,
	}
)

func (x RefundPaymentStatus) Enum() *RefundPaymentStatus {
	p := new(RefundPaymentStatus)
	*p = x
	return p
}

func (x RefundPaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RefundPaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v1_payment_enums_proto_enumTypes[1].Descriptor()
}

func (RefundPaymentStatus) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v1_payment_enums_proto_enumTypes[1]
}

func (x RefundPaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RefundPaymentStatus.Descriptor instead.
func (RefundPaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_enums_proto_rawDescGZIP(), []int{1}
}

// PaymentModule is the module of a payment
type PaymentModule int32

const (
	// unspecified
	PaymentModule_PAYMENT_MODULE_UNSPECIFIED PaymentModule = 0
	// grooming
	PaymentModule_GROOMING PaymentModule = 1
	// retail
	PaymentModule_RETAIL PaymentModule = 2
	// membership
	PaymentModule_MEMBERSHIP PaymentModule = 3
)

// Enum value maps for PaymentModule.
var (
	PaymentModule_name = map[int32]string{
		0: "PAYMENT_MODULE_UNSPECIFIED",
		1: "GROOMING",
		2: "RETAIL",
		3: "MEMBERSHIP",
	}
	PaymentModule_value = map[string]int32{
		"PAYMENT_MODULE_UNSPECIFIED": 0,
		"GROOMING":                   1,
		"RETAIL":                     2,
		"MEMBERSHIP":                 3,
	}
)

func (x PaymentModule) Enum() *PaymentModule {
	p := new(PaymentModule)
	*p = x
	return p
}

func (x PaymentModule) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentModule) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_models_payment_v1_payment_enums_proto_enumTypes[2].Descriptor()
}

func (PaymentModule) Type() protoreflect.EnumType {
	return &file_moego_models_payment_v1_payment_enums_proto_enumTypes[2]
}

func (x PaymentModule) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentModule.Descriptor instead.
func (PaymentModule) EnumDescriptor() ([]byte, []int) {
	return file_moego_models_payment_v1_payment_enums_proto_rawDescGZIP(), []int{2}
}

var File_moego_models_payment_v1_payment_enums_proto protoreflect.FileDescriptor

var file_moego_models_payment_v1_payment_enums_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2a, 0x5a, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49,
	0x4e, 0x47, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x41, 0x49, 0x44, 0x10, 0x02, 0x12, 0x0d,
	0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x13, 0x0a,
	0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0x01, 0x2a, 0xf7, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x75, 0x6e, 0x64, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x45,
	0x46, 0x55, 0x4e, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x24, 0x0a,
	0x20, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x49,
	0x44, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d,
	0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x1c, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x01, 0x12, 0x27, 0x0a, 0x1a, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x50, 0x41,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x10, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x2a, 0x59, 0x0a, 0x0d,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1e, 0x0a,
	0x1a, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x47, 0x52, 0x4f, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x52,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x45, 0x4d, 0x42, 0x45,
	0x52, 0x53, 0x48, 0x49, 0x50, 0x10, 0x03, 0x42, 0x7b, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x56, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_payment_v1_payment_enums_proto_rawDescOnce sync.Once
	file_moego_models_payment_v1_payment_enums_proto_rawDescData = file_moego_models_payment_v1_payment_enums_proto_rawDesc
)

func file_moego_models_payment_v1_payment_enums_proto_rawDescGZIP() []byte {
	file_moego_models_payment_v1_payment_enums_proto_rawDescOnce.Do(func() {
		file_moego_models_payment_v1_payment_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_payment_v1_payment_enums_proto_rawDescData)
	})
	return file_moego_models_payment_v1_payment_enums_proto_rawDescData
}

var file_moego_models_payment_v1_payment_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_moego_models_payment_v1_payment_enums_proto_goTypes = []interface{}{
	(PaymentStatus)(0),       // 0: moego.models.payment.v1.PaymentStatus
	(RefundPaymentStatus)(0), // 1: moego.models.payment.v1.RefundPaymentStatus
	(PaymentModule)(0),       // 2: moego.models.payment.v1.PaymentModule
}
var file_moego_models_payment_v1_payment_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_moego_models_payment_v1_payment_enums_proto_init() }
func file_moego_models_payment_v1_payment_enums_proto_init() {
	if File_moego_models_payment_v1_payment_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_payment_v1_payment_enums_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_payment_v1_payment_enums_proto_goTypes,
		DependencyIndexes: file_moego_models_payment_v1_payment_enums_proto_depIdxs,
		EnumInfos:         file_moego_models_payment_v1_payment_enums_proto_enumTypes,
	}.Build()
	File_moego_models_payment_v1_payment_enums_proto = out.File
	file_moego_models_payment_v1_payment_enums_proto_rawDesc = nil
	file_moego_models_payment_v1_payment_enums_proto_goTypes = nil
	file_moego_models_payment_v1_payment_enums_proto_depIdxs = nil
}
