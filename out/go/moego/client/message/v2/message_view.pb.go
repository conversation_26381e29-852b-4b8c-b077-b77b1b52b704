// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/message/v2/message_view.proto

package messageapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/message/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Sender view
type SenderView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 发起方的角色
	Role v2.Role `protobuf:"varint,1,opt,name=role,proto3,enum=moego.models.message.v2.Role" json:"role,omitempty"`
	// 发起方的 ID
	// 结合 role 决定是什么 ID
	Id uint64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// 头像 URL
	AvatarPath string `protobuf:"bytes,3,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// First Name
	FirstName string `protobuf:"bytes,4,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// Last Name
	LastName string `protobuf:"bytes,5,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *SenderView) Reset() {
	*x = SenderView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_message_v2_message_view_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SenderView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SenderView) ProtoMessage() {}

func (x *SenderView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_message_v2_message_view_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SenderView.ProtoReflect.Descriptor instead.
func (*SenderView) Descriptor() ([]byte, []int) {
	return file_moego_client_message_v2_message_view_proto_rawDescGZIP(), []int{0}
}

func (x *SenderView) GetRole() v2.Role {
	if x != nil {
		return x.Role
	}
	return v2.Role(0)
}

func (x *SenderView) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SenderView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *SenderView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *SenderView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

// Message view
type MessageView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 消息发起方
	Sender *SenderView `protobuf:"bytes,1,opt,name=sender,proto3" json:"sender,omitempty"`
	// 消息
	Message *v2.MessageModel `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *MessageView) Reset() {
	*x = MessageView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_message_v2_message_view_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MessageView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageView) ProtoMessage() {}

func (x *MessageView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_message_v2_message_view_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageView.ProtoReflect.Descriptor instead.
func (*MessageView) Descriptor() ([]byte, []int) {
	return file_moego_client_message_v2_message_view_proto_rawDescGZIP(), []int{1}
}

func (x *MessageView) GetSender() *SenderView {
	if x != nil {
		return x.Sender
	}
	return nil
}

func (x *MessageView) GetMessage() *v2.MessageModel {
	if x != nil {
		return x.Message
	}
	return nil
}

// Chat view
type ChatView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ------ COPY from ChatModel BEGIN -----
	// 对话 ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 对话所属 Business 的 Company ID
	CompanyId uint64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 对话所属 Business 的 ID
	BusinessId uint64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 对话所属 Customer 的 ID
	CustomerId uint64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 对话的状态
	ChatStatus v2.ChatStatus `protobuf:"varint,11,opt,name=chat_status,json=chatStatus,proto3,enum=moego.models.message.v2.ChatStatus" json:"chat_status,omitempty"`
	// 是否星标
	IsStarred bool `protobuf:"varint,12,opt,name=is_starred,json=isStarred,proto3" json:"is_starred,omitempty"`
	// 对话中最后一条消息的 ID
	LastMsgId uint64 `protobuf:"varint,21,opt,name=last_msg_id,json=lastMsgId,proto3" json:"last_msg_id,omitempty"`
	// 对话中最后一条消息的创建时间
	LastMsgCreateTime uint64 `protobuf:"varint,22,opt,name=last_msg_create_time,json=lastMsgCreateTime,proto3" json:"last_msg_create_time,omitempty"`
	// 打星标时间戳，毫秒
	StarTime uint64 `protobuf:"varint,31,opt,name=star_time,json=starTime,proto3" json:"star_time,omitempty"`
	// 封锁时间戳，毫秒
	BlockTime uint64 `protobuf:"varint,32,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	// 创建时间戳，毫秒
	CreateTime uint64 `protobuf:"varint,33,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间戳，毫秒
	UpdateTime uint64 `protobuf:"varint,34,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 最后一条消息
	LastMsg *MessageView `protobuf:"bytes,51,opt,name=last_msg,json=lastMsg,proto3" json:"last_msg,omitempty"`
	// Chat 的头像
	AvatarPath string `protobuf:"bytes,52,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// Chat 的名字
	Name string `protobuf:"bytes,53,opt,name=name,proto3" json:"name,omitempty"`
	// Chat 中未读消息的数量
	UnreadMessageCount uint64 `protobuf:"varint,54,opt,name=unread_message_count,json=unreadMessageCount,proto3" json:"unread_message_count,omitempty"`
}

func (x *ChatView) Reset() {
	*x = ChatView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_message_v2_message_view_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChatView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatView) ProtoMessage() {}

func (x *ChatView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_message_v2_message_view_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatView.ProtoReflect.Descriptor instead.
func (*ChatView) Descriptor() ([]byte, []int) {
	return file_moego_client_message_v2_message_view_proto_rawDescGZIP(), []int{2}
}

func (x *ChatView) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatView) GetCompanyId() uint64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ChatView) GetBusinessId() uint64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ChatView) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ChatView) GetChatStatus() v2.ChatStatus {
	if x != nil {
		return x.ChatStatus
	}
	return v2.ChatStatus(0)
}

func (x *ChatView) GetIsStarred() bool {
	if x != nil {
		return x.IsStarred
	}
	return false
}

func (x *ChatView) GetLastMsgId() uint64 {
	if x != nil {
		return x.LastMsgId
	}
	return 0
}

func (x *ChatView) GetLastMsgCreateTime() uint64 {
	if x != nil {
		return x.LastMsgCreateTime
	}
	return 0
}

func (x *ChatView) GetStarTime() uint64 {
	if x != nil {
		return x.StarTime
	}
	return 0
}

func (x *ChatView) GetBlockTime() uint64 {
	if x != nil {
		return x.BlockTime
	}
	return 0
}

func (x *ChatView) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *ChatView) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *ChatView) GetLastMsg() *MessageView {
	if x != nil {
		return x.LastMsg
	}
	return nil
}

func (x *ChatView) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *ChatView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChatView) GetUnreadMessageCount() uint64 {
	if x != nil {
		return x.UnreadMessageCount
	}
	return 0
}

var File_moego_client_message_v2_message_view_proto protoreflect.FileDescriptor

var file_moego_client_message_v2_message_view_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x32, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xac, 0x01, 0x0a, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x31, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x8b, 0x01, 0x0a, 0x0b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x3b, 0x0a, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xd7, 0x04,
	0x0a, 0x08, 0x43, 0x68, 0x61, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0b, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x72, 0x65, 0x64, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x53, 0x74, 0x61, 0x72, 0x72, 0x65, 0x64,
	0x12, 0x1e, 0x0a, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x73, 0x67, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d, 0x73, 0x67, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x04, 0x52, 0x11,
	0x6c, 0x61, 0x73, 0x74, 0x4d, 0x73, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1f,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x22, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x3f, 0x0a, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x33, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x07, 0x6c, 0x61, 0x73, 0x74, 0x4d, 0x73, 0x67,
	0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x5f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x36, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x12, 0x75, 0x6e, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x7e, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x32, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69,
	0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d,
	0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x76, 0x32, 0x3b, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_message_v2_message_view_proto_rawDescOnce sync.Once
	file_moego_client_message_v2_message_view_proto_rawDescData = file_moego_client_message_v2_message_view_proto_rawDesc
)

func file_moego_client_message_v2_message_view_proto_rawDescGZIP() []byte {
	file_moego_client_message_v2_message_view_proto_rawDescOnce.Do(func() {
		file_moego_client_message_v2_message_view_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_message_v2_message_view_proto_rawDescData)
	})
	return file_moego_client_message_v2_message_view_proto_rawDescData
}

var file_moego_client_message_v2_message_view_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_client_message_v2_message_view_proto_goTypes = []interface{}{
	(*SenderView)(nil),      // 0: moego.client.message.v2.SenderView
	(*MessageView)(nil),     // 1: moego.client.message.v2.MessageView
	(*ChatView)(nil),        // 2: moego.client.message.v2.ChatView
	(v2.Role)(0),            // 3: moego.models.message.v2.Role
	(*v2.MessageModel)(nil), // 4: moego.models.message.v2.MessageModel
	(v2.ChatStatus)(0),      // 5: moego.models.message.v2.ChatStatus
}
var file_moego_client_message_v2_message_view_proto_depIdxs = []int32{
	3, // 0: moego.client.message.v2.SenderView.role:type_name -> moego.models.message.v2.Role
	0, // 1: moego.client.message.v2.MessageView.sender:type_name -> moego.client.message.v2.SenderView
	4, // 2: moego.client.message.v2.MessageView.message:type_name -> moego.models.message.v2.MessageModel
	5, // 3: moego.client.message.v2.ChatView.chat_status:type_name -> moego.models.message.v2.ChatStatus
	1, // 4: moego.client.message.v2.ChatView.last_msg:type_name -> moego.client.message.v2.MessageView
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_moego_client_message_v2_message_view_proto_init() }
func file_moego_client_message_v2_message_view_proto_init() {
	if File_moego_client_message_v2_message_view_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_message_v2_message_view_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SenderView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_message_v2_message_view_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MessageView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_message_v2_message_view_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChatView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_message_v2_message_view_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_client_message_v2_message_view_proto_goTypes,
		DependencyIndexes: file_moego_client_message_v2_message_view_proto_depIdxs,
		MessageInfos:      file_moego_client_message_v2_message_view_proto_msgTypes,
	}.Build()
	File_moego_client_message_v2_message_view_proto = out.File
	file_moego_client_message_v2_message_view_proto_rawDesc = nil
	file_moego_client_message_v2_message_view_proto_goTypes = nil
	file_moego_client_message_v2_message_view_proto_depIdxs = nil
}
