// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/client/account/v1/account_preference_api.proto

package accountapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get account preference params
type GetAccountPreferenceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Selected company id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
}

func (x *GetAccountPreferenceParams) Reset() {
	*x = GetAccountPreferenceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_preference_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountPreferenceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountPreferenceParams) ProtoMessage() {}

func (x *GetAccountPreferenceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_preference_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountPreferenceParams.ProtoReflect.Descriptor instead.
func (*GetAccountPreferenceParams) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_preference_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetAccountPreferenceParams) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// get account preference result
type GetAccountPreferenceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// preference setting
	PreferenceSetting *CompanyPreferenceSettingView `protobuf:"bytes,1,opt,name=preference_setting,json=preferenceSetting,proto3" json:"preference_setting,omitempty"`
}

func (x *GetAccountPreferenceResult) Reset() {
	*x = GetAccountPreferenceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_preference_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountPreferenceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountPreferenceResult) ProtoMessage() {}

func (x *GetAccountPreferenceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_preference_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountPreferenceResult.ProtoReflect.Descriptor instead.
func (*GetAccountPreferenceResult) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_preference_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetAccountPreferenceResult) GetPreferenceSetting() *CompanyPreferenceSettingView {
	if x != nil {
		return x.PreferenceSetting
	}
	return nil
}

// company preference setting view
type CompanyPreferenceSettingView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// currency code
	CurrencyCode string `protobuf:"bytes,1,opt,name=currency_code,json=currencyCode,proto3" json:"currency_code,omitempty"`
	// currency symbol
	CurrencySymbol string `protobuf:"bytes,2,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// date format
	DateFormatType v1.DateFormat `protobuf:"varint,3,opt,name=date_format_type,json=dateFormatType,proto3,enum=moego.models.organization.v1.DateFormat" json:"date_format_type,omitempty"`
	// time format
	TimeFormatType v1.TimeFormat `protobuf:"varint,4,opt,name=time_format_type,json=timeFormatType,proto3,enum=moego.models.organization.v1.TimeFormat" json:"time_format_type,omitempty"`
	// unit of weight
	UnitOfWeightType v1.WeightUnit `protobuf:"varint,5,opt,name=unit_of_weight_type,json=unitOfWeightType,proto3,enum=moego.models.organization.v1.WeightUnit" json:"unit_of_weight_type,omitempty"`
	// unit of distance
	UnitOfDistanceType v1.DistanceUnit `protobuf:"varint,6,opt,name=unit_of_distance_type,json=unitOfDistanceType,proto3,enum=moego.models.organization.v1.DistanceUnit" json:"unit_of_distance_type,omitempty"`
	// country
	Country *v1.CountryDef `protobuf:"bytes,8,opt,name=country,proto3" json:"country,omitempty"`
	// timezone
	TimeZone *v1.TimeZone `protobuf:"bytes,9,opt,name=time_zone,json=timeZone,proto3" json:"time_zone,omitempty"`
}

func (x *CompanyPreferenceSettingView) Reset() {
	*x = CompanyPreferenceSettingView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_client_account_v1_account_preference_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompanyPreferenceSettingView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyPreferenceSettingView) ProtoMessage() {}

func (x *CompanyPreferenceSettingView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_client_account_v1_account_preference_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyPreferenceSettingView.ProtoReflect.Descriptor instead.
func (*CompanyPreferenceSettingView) Descriptor() ([]byte, []int) {
	return file_moego_client_account_v1_account_preference_api_proto_rawDescGZIP(), []int{2}
}

func (x *CompanyPreferenceSettingView) GetCurrencyCode() string {
	if x != nil {
		return x.CurrencyCode
	}
	return ""
}

func (x *CompanyPreferenceSettingView) GetCurrencySymbol() string {
	if x != nil {
		return x.CurrencySymbol
	}
	return ""
}

func (x *CompanyPreferenceSettingView) GetDateFormatType() v1.DateFormat {
	if x != nil {
		return x.DateFormatType
	}
	return v1.DateFormat(0)
}

func (x *CompanyPreferenceSettingView) GetTimeFormatType() v1.TimeFormat {
	if x != nil {
		return x.TimeFormatType
	}
	return v1.TimeFormat(0)
}

func (x *CompanyPreferenceSettingView) GetUnitOfWeightType() v1.WeightUnit {
	if x != nil {
		return x.UnitOfWeightType
	}
	return v1.WeightUnit(0)
}

func (x *CompanyPreferenceSettingView) GetUnitOfDistanceType() v1.DistanceUnit {
	if x != nil {
		return x.UnitOfDistanceType
	}
	return v1.DistanceUnit(0)
}

func (x *CompanyPreferenceSettingView) GetCountry() *v1.CountryDef {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *CompanyPreferenceSettingView) GetTimeZone() *v1.TimeZone {
	if x != nil {
		return x.TimeZone
	}
	return nil
}

var File_moego_client_account_v1_account_preference_api_proto protoreflect.FileDescriptor

var file_moego_client_account_v1_account_preference_api_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x70, 0x69,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a,
	0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x58, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2b, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x69, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x64, 0x0a, 0x12, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x11, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x22, 0xd5, 0x04, 0x0a, 0x1c, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x52, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x0e, 0x64, 0x61, 0x74,
	0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x10, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52,
	0x0e, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x57, 0x0a, 0x13, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x66, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x57, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5d, 0x0a, 0x15, 0x75, 0x6e, 0x69, 0x74,
	0x5f, 0x6f, 0x66, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x69, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x55,
	0x6e, 0x69, 0x74, 0x52, 0x12, 0x75, 0x6e, 0x69, 0x74, 0x4f, 0x66, 0x44, 0x69, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x44,
	0x65, 0x66, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x43, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x5a, 0x6f, 0x6e, 0x65,
	0x32, 0x9d, 0x01, 0x0a, 0x18, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x80, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x42, 0x7e, 0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x59, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2f, 0x76, 0x31, 0x3b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_client_account_v1_account_preference_api_proto_rawDescOnce sync.Once
	file_moego_client_account_v1_account_preference_api_proto_rawDescData = file_moego_client_account_v1_account_preference_api_proto_rawDesc
)

func file_moego_client_account_v1_account_preference_api_proto_rawDescGZIP() []byte {
	file_moego_client_account_v1_account_preference_api_proto_rawDescOnce.Do(func() {
		file_moego_client_account_v1_account_preference_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_client_account_v1_account_preference_api_proto_rawDescData)
	})
	return file_moego_client_account_v1_account_preference_api_proto_rawDescData
}

var file_moego_client_account_v1_account_preference_api_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_client_account_v1_account_preference_api_proto_goTypes = []interface{}{
	(*GetAccountPreferenceParams)(nil),   // 0: moego.client.account.v1.GetAccountPreferenceParams
	(*GetAccountPreferenceResult)(nil),   // 1: moego.client.account.v1.GetAccountPreferenceResult
	(*CompanyPreferenceSettingView)(nil), // 2: moego.client.account.v1.CompanyPreferenceSettingView
	(v1.DateFormat)(0),                   // 3: moego.models.organization.v1.DateFormat
	(v1.TimeFormat)(0),                   // 4: moego.models.organization.v1.TimeFormat
	(v1.WeightUnit)(0),                   // 5: moego.models.organization.v1.WeightUnit
	(v1.DistanceUnit)(0),                 // 6: moego.models.organization.v1.DistanceUnit
	(*v1.CountryDef)(nil),                // 7: moego.models.organization.v1.CountryDef
	(*v1.TimeZone)(nil),                  // 8: moego.models.organization.v1.TimeZone
}
var file_moego_client_account_v1_account_preference_api_proto_depIdxs = []int32{
	2, // 0: moego.client.account.v1.GetAccountPreferenceResult.preference_setting:type_name -> moego.client.account.v1.CompanyPreferenceSettingView
	3, // 1: moego.client.account.v1.CompanyPreferenceSettingView.date_format_type:type_name -> moego.models.organization.v1.DateFormat
	4, // 2: moego.client.account.v1.CompanyPreferenceSettingView.time_format_type:type_name -> moego.models.organization.v1.TimeFormat
	5, // 3: moego.client.account.v1.CompanyPreferenceSettingView.unit_of_weight_type:type_name -> moego.models.organization.v1.WeightUnit
	6, // 4: moego.client.account.v1.CompanyPreferenceSettingView.unit_of_distance_type:type_name -> moego.models.organization.v1.DistanceUnit
	7, // 5: moego.client.account.v1.CompanyPreferenceSettingView.country:type_name -> moego.models.organization.v1.CountryDef
	8, // 6: moego.client.account.v1.CompanyPreferenceSettingView.time_zone:type_name -> moego.models.organization.v1.TimeZone
	0, // 7: moego.client.account.v1.AccountPreferenceService.GetAccountPreference:input_type -> moego.client.account.v1.GetAccountPreferenceParams
	1, // 8: moego.client.account.v1.AccountPreferenceService.GetAccountPreference:output_type -> moego.client.account.v1.GetAccountPreferenceResult
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_client_account_v1_account_preference_api_proto_init() }
func file_moego_client_account_v1_account_preference_api_proto_init() {
	if File_moego_client_account_v1_account_preference_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_client_account_v1_account_preference_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountPreferenceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_preference_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountPreferenceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_client_account_v1_account_preference_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompanyPreferenceSettingView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_client_account_v1_account_preference_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_client_account_v1_account_preference_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_client_account_v1_account_preference_api_proto_goTypes,
		DependencyIndexes: file_moego_client_account_v1_account_preference_api_proto_depIdxs,
		MessageInfos:      file_moego_client_account_v1_account_preference_api_proto_msgTypes,
	}.Build()
	File_moego_client_account_v1_account_preference_api_proto = out.File
	file_moego_client_account_v1_account_preference_api_proto_rawDesc = nil
	file_moego_client_account_v1_account_preference_api_proto_goTypes = nil
	file_moego_client_account_v1_account_preference_api_proto_depIdxs = nil
}
