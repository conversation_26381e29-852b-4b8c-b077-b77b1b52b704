// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/appointment/v1/pet_playgroup_api.proto

package appointmentapipb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list playgroup calendar view params
type ListPlaygroupCalendarViewParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// playgroup model pagination
	PlaygroupPagination *v2.PaginationRequest `protobuf:"bytes,4,opt,name=playgroup_pagination,json=playgroupPagination,proto3" json:"playgroup_pagination,omitempty"`
}

func (x *ListPlaygroupCalendarViewParams) Reset() {
	*x = ListPlaygroupCalendarViewParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupCalendarViewParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupCalendarViewParams) ProtoMessage() {}

func (x *ListPlaygroupCalendarViewParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupCalendarViewParams.ProtoReflect.Descriptor instead.
func (*ListPlaygroupCalendarViewParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListPlaygroupCalendarViewParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ListPlaygroupCalendarViewParams) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ListPlaygroupCalendarViewParams) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ListPlaygroupCalendarViewParams) GetPlaygroupPagination() *v2.PaginationRequest {
	if x != nil {
		return x.PlaygroupPagination
	}
	return nil
}

// list playgroup calendar view result
type ListPlaygroupCalendarViewResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup model list
	Playgroups []*v1.PlaygroupModel `protobuf:"bytes,1,rep,name=playgroups,proto3" json:"playgroups,omitempty"`
	// pet playgroup daily view list
	PlaygroupDailies []*ListPlaygroupCalendarViewResult_PlaygroupDailyView `protobuf:"bytes,2,rep,name=playgroup_dailies,json=playgroupDailies,proto3" json:"playgroup_dailies,omitempty"`
	// pet view list
	Pets []*ListPlaygroupCalendarViewResult_PetView `protobuf:"bytes,3,rep,name=pets,proto3" json:"pets,omitempty"`
	// appointment view list
	Appointments []*ListPlaygroupCalendarViewResult_AppointmentView `protobuf:"bytes,4,rep,name=appointments,proto3" json:"appointments,omitempty"`
	// playgroup model pagination
	PlaygroupPagination *v2.PaginationResponse `protobuf:"bytes,5,opt,name=playgroup_pagination,json=playgroupPagination,proto3" json:"playgroup_pagination,omitempty"`
}

func (x *ListPlaygroupCalendarViewResult) Reset() {
	*x = ListPlaygroupCalendarViewResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupCalendarViewResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupCalendarViewResult) ProtoMessage() {}

func (x *ListPlaygroupCalendarViewResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupCalendarViewResult.ProtoReflect.Descriptor instead.
func (*ListPlaygroupCalendarViewResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPlaygroupCalendarViewResult) GetPlaygroups() []*v1.PlaygroupModel {
	if x != nil {
		return x.Playgroups
	}
	return nil
}

func (x *ListPlaygroupCalendarViewResult) GetPlaygroupDailies() []*ListPlaygroupCalendarViewResult_PlaygroupDailyView {
	if x != nil {
		return x.PlaygroupDailies
	}
	return nil
}

func (x *ListPlaygroupCalendarViewResult) GetPets() []*ListPlaygroupCalendarViewResult_PetView {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *ListPlaygroupCalendarViewResult) GetAppointments() []*ListPlaygroupCalendarViewResult_AppointmentView {
	if x != nil {
		return x.Appointments
	}
	return nil
}

func (x *ListPlaygroupCalendarViewResult) GetPlaygroupPagination() *v2.PaginationResponse {
	if x != nil {
		return x.PlaygroupPagination
	}
	return nil
}

// reschedule pet playgroup
type ReschedulePetPlaygroupParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// business id
	BusinessId int64 `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// pet playgroup id
	PetPlaygroupId int64 `protobuf:"varint,3,opt,name=pet_playgroup_id,json=petPlaygroupId,proto3" json:"pet_playgroup_id,omitempty"`
	// target playgroup id
	PlaygroupId int64 `protobuf:"varint,2,opt,name=playgroup_id,json=playgroupId,proto3" json:"playgroup_id,omitempty"`
	// target date
	Date *date.Date `protobuf:"bytes,5,opt,name=date,proto3" json:"date,omitempty"`
	// target index
	Index int32 `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty"`
}

func (x *ReschedulePetPlaygroupParams) Reset() {
	*x = ReschedulePetPlaygroupParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReschedulePetPlaygroupParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReschedulePetPlaygroupParams) ProtoMessage() {}

func (x *ReschedulePetPlaygroupParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReschedulePetPlaygroupParams.ProtoReflect.Descriptor instead.
func (*ReschedulePetPlaygroupParams) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{2}
}

func (x *ReschedulePetPlaygroupParams) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *ReschedulePetPlaygroupParams) GetPetPlaygroupId() int64 {
	if x != nil {
		return x.PetPlaygroupId
	}
	return 0
}

func (x *ReschedulePetPlaygroupParams) GetPlaygroupId() int64 {
	if x != nil {
		return x.PlaygroupId
	}
	return 0
}

func (x *ReschedulePetPlaygroupParams) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ReschedulePetPlaygroupParams) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

// reschedule pet playgroup result
type ReschedulePetPlaygroupResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReschedulePetPlaygroupResult) Reset() {
	*x = ReschedulePetPlaygroupResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReschedulePetPlaygroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReschedulePetPlaygroupResult) ProtoMessage() {}

func (x *ReschedulePetPlaygroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReschedulePetPlaygroupResult.ProtoReflect.Descriptor instead.
func (*ReschedulePetPlaygroupResult) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{3}
}

// playgroup daily view
type ListPlaygroupCalendarViewResult_PlaygroupDailyView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date
	Date *date.Date `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// playgroup view
	Playgroups []*ListPlaygroupCalendarViewResult_PlaygroupView `protobuf:"bytes,2,rep,name=playgroups,proto3" json:"playgroups,omitempty"`
}

func (x *ListPlaygroupCalendarViewResult_PlaygroupDailyView) Reset() {
	*x = ListPlaygroupCalendarViewResult_PlaygroupDailyView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupCalendarViewResult_PlaygroupDailyView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupCalendarViewResult_PlaygroupDailyView) ProtoMessage() {}

func (x *ListPlaygroupCalendarViewResult_PlaygroupDailyView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupCalendarViewResult_PlaygroupDailyView.ProtoReflect.Descriptor instead.
func (*ListPlaygroupCalendarViewResult_PlaygroupDailyView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListPlaygroupCalendarViewResult_PlaygroupDailyView) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *ListPlaygroupCalendarViewResult_PlaygroupDailyView) GetPlaygroups() []*ListPlaygroupCalendarViewResult_PlaygroupView {
	if x != nil {
		return x.Playgroups
	}
	return nil
}

// playgroup view
type ListPlaygroupCalendarViewResult_PlaygroupView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// playgroup id
	PlaygroupId int64 `protobuf:"varint,1,opt,name=playgroup_id,json=playgroupId,proto3" json:"playgroup_id,omitempty"`
	// pet number
	PetNumber int32 `protobuf:"varint,2,opt,name=pet_number,json=petNumber,proto3" json:"pet_number,omitempty"`
	// pet id list
	PetPlaygroups []*ListPlaygroupCalendarViewResult_PetPlaygroupView `protobuf:"bytes,3,rep,name=pet_playgroups,json=petPlaygroups,proto3" json:"pet_playgroups,omitempty"`
}

func (x *ListPlaygroupCalendarViewResult_PlaygroupView) Reset() {
	*x = ListPlaygroupCalendarViewResult_PlaygroupView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupCalendarViewResult_PlaygroupView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupCalendarViewResult_PlaygroupView) ProtoMessage() {}

func (x *ListPlaygroupCalendarViewResult_PlaygroupView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupCalendarViewResult_PlaygroupView.ProtoReflect.Descriptor instead.
func (*ListPlaygroupCalendarViewResult_PlaygroupView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{1, 1}
}

func (x *ListPlaygroupCalendarViewResult_PlaygroupView) GetPlaygroupId() int64 {
	if x != nil {
		return x.PlaygroupId
	}
	return 0
}

func (x *ListPlaygroupCalendarViewResult_PlaygroupView) GetPetNumber() int32 {
	if x != nil {
		return x.PetNumber
	}
	return 0
}

func (x *ListPlaygroupCalendarViewResult_PlaygroupView) GetPetPlaygroups() []*ListPlaygroupCalendarViewResult_PetPlaygroupView {
	if x != nil {
		return x.PetPlaygroups
	}
	return nil
}

// pet group view
type ListPlaygroupCalendarViewResult_PetPlaygroupView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet playgroup id
	PetPlaygroupId int64 `protobuf:"varint,1,opt,name=pet_playgroup_id,json=petPlaygroupId,proto3" json:"pet_playgroup_id,omitempty"`
	// pet id
	PetId int64 `protobuf:"varint,2,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// appointment id
	AppointmentId int64 `protobuf:"varint,3,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// pet playgroup list sort. start with 1 and put the smallest first
	Sort int32 `protobuf:"varint,4,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *ListPlaygroupCalendarViewResult_PetPlaygroupView) Reset() {
	*x = ListPlaygroupCalendarViewResult_PetPlaygroupView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupCalendarViewResult_PetPlaygroupView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupCalendarViewResult_PetPlaygroupView) ProtoMessage() {}

func (x *ListPlaygroupCalendarViewResult_PetPlaygroupView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupCalendarViewResult_PetPlaygroupView.ProtoReflect.Descriptor instead.
func (*ListPlaygroupCalendarViewResult_PetPlaygroupView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{1, 2}
}

func (x *ListPlaygroupCalendarViewResult_PetPlaygroupView) GetPetPlaygroupId() int64 {
	if x != nil {
		return x.PetPlaygroupId
	}
	return 0
}

func (x *ListPlaygroupCalendarViewResult_PetPlaygroupView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListPlaygroupCalendarViewResult_PetPlaygroupView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListPlaygroupCalendarViewResult_PetPlaygroupView) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// pet view
type ListPlaygroupCalendarViewResult_PetView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet id
	PetId int64 `protobuf:"varint,1,opt,name=pet_id,json=petId,proto3" json:"pet_id,omitempty"`
	// pet name
	PetName string `protobuf:"bytes,2,opt,name=pet_name,json=petName,proto3" json:"pet_name,omitempty"`
	// pet image
	PetImage string `protobuf:"bytes,3,opt,name=pet_image,json=petImage,proto3" json:"pet_image,omitempty"`
	// pet type
	PetType v11.PetType `protobuf:"varint,4,opt,name=pet_type,json=petType,proto3,enum=moego.models.customer.v1.PetType" json:"pet_type,omitempty"`
	// pet playgroup color
	PetPlaygroupColor string `protobuf:"bytes,5,opt,name=pet_playgroup_color,json=petPlaygroupColor,proto3" json:"pet_playgroup_color,omitempty"`
	// vaccine binding list
	Vaccines []*VaccineComposite `protobuf:"bytes,6,rep,name=vaccines,proto3" json:"vaccines,omitempty"`
	// enable vaccine expiry notification, ture is enable, false is disable
	EnableVaccineExpiryNotification bool `protobuf:"varint,7,opt,name=enable_vaccine_expiry_notification,json=enableVaccineExpiryNotification,proto3" json:"enable_vaccine_expiry_notification,omitempty"`
	// customer
	Customer *ListPlaygroupCalendarViewResult_CustomerView `protobuf:"bytes,8,opt,name=customer,proto3" json:"customer,omitempty"`
}

func (x *ListPlaygroupCalendarViewResult_PetView) Reset() {
	*x = ListPlaygroupCalendarViewResult_PetView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupCalendarViewResult_PetView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupCalendarViewResult_PetView) ProtoMessage() {}

func (x *ListPlaygroupCalendarViewResult_PetView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupCalendarViewResult_PetView.ProtoReflect.Descriptor instead.
func (*ListPlaygroupCalendarViewResult_PetView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{1, 3}
}

func (x *ListPlaygroupCalendarViewResult_PetView) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ListPlaygroupCalendarViewResult_PetView) GetPetName() string {
	if x != nil {
		return x.PetName
	}
	return ""
}

func (x *ListPlaygroupCalendarViewResult_PetView) GetPetImage() string {
	if x != nil {
		return x.PetImage
	}
	return ""
}

func (x *ListPlaygroupCalendarViewResult_PetView) GetPetType() v11.PetType {
	if x != nil {
		return x.PetType
	}
	return v11.PetType(0)
}

func (x *ListPlaygroupCalendarViewResult_PetView) GetPetPlaygroupColor() string {
	if x != nil {
		return x.PetPlaygroupColor
	}
	return ""
}

func (x *ListPlaygroupCalendarViewResult_PetView) GetVaccines() []*VaccineComposite {
	if x != nil {
		return x.Vaccines
	}
	return nil
}

func (x *ListPlaygroupCalendarViewResult_PetView) GetEnableVaccineExpiryNotification() bool {
	if x != nil {
		return x.EnableVaccineExpiryNotification
	}
	return false
}

func (x *ListPlaygroupCalendarViewResult_PetView) GetCustomer() *ListPlaygroupCalendarViewResult_CustomerView {
	if x != nil {
		return x.Customer
	}
	return nil
}

// appointment view
type ListPlaygroupCalendarViewResult_AppointmentView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// appointment id
	AppointmentId int64 `protobuf:"varint,1,opt,name=appointment_id,json=appointmentId,proto3" json:"appointment_id,omitempty"`
	// appointment status
	AppointmentStatus v12.AppointmentStatus `protobuf:"varint,2,opt,name=appointment_status,json=appointmentStatus,proto3,enum=moego.models.appointment.v1.AppointmentStatus" json:"appointment_status,omitempty"`
	// service item types
	ServiceItemTypes []v1.ServiceItemType `protobuf:"varint,3,rep,packed,name=service_item_types,json=serviceItemTypes,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_types,omitempty"`
	// appointment pet ids
	PetIds []int64 `protobuf:"varint,4,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// appointment start date
	AppointmentStartDate *date.Date `protobuf:"bytes,5,opt,name=appointment_start_date,json=appointmentStartDate,proto3" json:"appointment_start_date,omitempty"`
}

func (x *ListPlaygroupCalendarViewResult_AppointmentView) Reset() {
	*x = ListPlaygroupCalendarViewResult_AppointmentView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupCalendarViewResult_AppointmentView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupCalendarViewResult_AppointmentView) ProtoMessage() {}

func (x *ListPlaygroupCalendarViewResult_AppointmentView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupCalendarViewResult_AppointmentView.ProtoReflect.Descriptor instead.
func (*ListPlaygroupCalendarViewResult_AppointmentView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{1, 4}
}

func (x *ListPlaygroupCalendarViewResult_AppointmentView) GetAppointmentId() int64 {
	if x != nil {
		return x.AppointmentId
	}
	return 0
}

func (x *ListPlaygroupCalendarViewResult_AppointmentView) GetAppointmentStatus() v12.AppointmentStatus {
	if x != nil {
		return x.AppointmentStatus
	}
	return v12.AppointmentStatus(0)
}

func (x *ListPlaygroupCalendarViewResult_AppointmentView) GetServiceItemTypes() []v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemTypes
	}
	return nil
}

func (x *ListPlaygroupCalendarViewResult_AppointmentView) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ListPlaygroupCalendarViewResult_AppointmentView) GetAppointmentStartDate() *date.Date {
	if x != nil {
		return x.AppointmentStartDate
	}
	return nil
}

// customer view
type ListPlaygroupCalendarViewResult_CustomerView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// customer id
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// first name
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// last name
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
}

func (x *ListPlaygroupCalendarViewResult_CustomerView) Reset() {
	*x = ListPlaygroupCalendarViewResult_CustomerView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPlaygroupCalendarViewResult_CustomerView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlaygroupCalendarViewResult_CustomerView) ProtoMessage() {}

func (x *ListPlaygroupCalendarViewResult_CustomerView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlaygroupCalendarViewResult_CustomerView.ProtoReflect.Descriptor instead.
func (*ListPlaygroupCalendarViewResult_CustomerView) Descriptor() ([]byte, []int) {
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP(), []int{1, 5}
}

func (x *ListPlaygroupCalendarViewResult_CustomerView) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *ListPlaygroupCalendarViewResult_CustomerView) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *ListPlaygroupCalendarViewResult_CustomerView) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

var File_moego_api_appointment_v1_pet_playgroup_api_proto protoreflect.FileDescriptor

var file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x70,
	0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x76, 0x65, 0x72, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31,
	0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75,
	0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x95, 0x02, 0x0a, 0x1f, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28,
	0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75,
	0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x54, 0x0a, 0x14,
	0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x13, 0x70,
	0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x86, 0x0f, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x12, 0x79, 0x0a, 0x11, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x64, 0x61,
	0x69, 0x6c, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x56, 0x69, 0x65, 0x77, 0x52, 0x10, 0x70, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x44, 0x61, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x12, 0x55, 0x0a, 0x04, 0x70,
	0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x04, 0x70, 0x65,
	0x74, 0x73, 0x12, 0x6d, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x55, 0x0a, 0x14, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x13, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0xa4, 0x01, 0x0a, 0x12, 0x50, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x56, 0x69, 0x65, 0x77, 0x12,
	0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x67, 0x0a, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x1a,
	0xc4, 0x01, 0x0a, 0x0d, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x69, 0x65,
	0x77, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x65, 0x74, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x71, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x50, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0d, 0x70, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x1a, 0x8e, 0x01, 0x0a, 0x10, 0x50, 0x65, 0x74, 0x50, 0x6c,
	0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x56, 0x69, 0x65, 0x77, 0x12, 0x28, 0x0a, 0x10, 0x70,
	0x65, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x1a, 0xbf, 0x03, 0x0a, 0x07, 0x50, 0x65, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x12, 0x15, 0x0a, 0x06, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x65,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x65, 0x74, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x70, 0x65, 0x74, 0x5f, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70,
	0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x46, 0x0a, 0x08, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x61,
	0x63, 0x63, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x65, 0x52, 0x08,
	0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x22, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x76, 0x61, 0x63, 0x63, 0x69, 0x6e, 0x65, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72,
	0x79, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x1f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x63, 0x63,
	0x69, 0x6e, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x1a, 0xd2, 0x02, 0x0a, 0x0f, 0x41, 0x70,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x11, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x57, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07,
	0x70, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x06, 0x70,
	0x65, 0x74, 0x49, 0x64, 0x73, 0x12, 0x47, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x14, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x1a, 0x6b,
	0x0a, 0x0c, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf7, 0x01, 0x0a, 0x1c,
	0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x0b,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x10, 0x70, 0x65, 0x74, 0x5f, 0x70, 0x6c,
	0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x70, 0x65, 0x74, 0x50, 0x6c,
	0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0c, 0x70, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x1e, 0x0a, 0x1c, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x32, 0xb8, 0x02, 0x0a, 0x13, 0x50, 0x65, 0x74, 0x50, 0x6c, 0x61,
	0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x93, 0x01,
	0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x12, 0x39, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x00, 0x12, 0x8a, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x50, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x36,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x65, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x50,
	0x6c, 0x61, 0x79, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00,
	0x42, 0x84, 0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescOnce sync.Once
	file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescData = file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDesc
)

func file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescGZIP() []byte {
	file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescOnce.Do(func() {
		file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescData)
	})
	return file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDescData
}

var file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_moego_api_appointment_v1_pet_playgroup_api_proto_goTypes = []interface{}{
	(*ListPlaygroupCalendarViewParams)(nil),                    // 0: moego.api.appointment.v1.ListPlaygroupCalendarViewParams
	(*ListPlaygroupCalendarViewResult)(nil),                    // 1: moego.api.appointment.v1.ListPlaygroupCalendarViewResult
	(*ReschedulePetPlaygroupParams)(nil),                       // 2: moego.api.appointment.v1.ReschedulePetPlaygroupParams
	(*ReschedulePetPlaygroupResult)(nil),                       // 3: moego.api.appointment.v1.ReschedulePetPlaygroupResult
	(*ListPlaygroupCalendarViewResult_PlaygroupDailyView)(nil), // 4: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PlaygroupDailyView
	(*ListPlaygroupCalendarViewResult_PlaygroupView)(nil),      // 5: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PlaygroupView
	(*ListPlaygroupCalendarViewResult_PetPlaygroupView)(nil),   // 6: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PetPlaygroupView
	(*ListPlaygroupCalendarViewResult_PetView)(nil),            // 7: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PetView
	(*ListPlaygroupCalendarViewResult_AppointmentView)(nil),    // 8: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.AppointmentView
	(*ListPlaygroupCalendarViewResult_CustomerView)(nil),       // 9: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.CustomerView
	(*date.Date)(nil),             // 10: google.type.Date
	(*v2.PaginationRequest)(nil),  // 11: moego.utils.v2.PaginationRequest
	(*v1.PlaygroupModel)(nil),     // 12: moego.models.offering.v1.PlaygroupModel
	(*v2.PaginationResponse)(nil), // 13: moego.utils.v2.PaginationResponse
	(v11.PetType)(0),              // 14: moego.models.customer.v1.PetType
	(*VaccineComposite)(nil),      // 15: moego.api.appointment.v1.VaccineComposite
	(v12.AppointmentStatus)(0),    // 16: moego.models.appointment.v1.AppointmentStatus
	(v1.ServiceItemType)(0),       // 17: moego.models.offering.v1.ServiceItemType
}
var file_moego_api_appointment_v1_pet_playgroup_api_proto_depIdxs = []int32{
	10, // 0: moego.api.appointment.v1.ListPlaygroupCalendarViewParams.start_date:type_name -> google.type.Date
	10, // 1: moego.api.appointment.v1.ListPlaygroupCalendarViewParams.end_date:type_name -> google.type.Date
	11, // 2: moego.api.appointment.v1.ListPlaygroupCalendarViewParams.playgroup_pagination:type_name -> moego.utils.v2.PaginationRequest
	12, // 3: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.playgroups:type_name -> moego.models.offering.v1.PlaygroupModel
	4,  // 4: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.playgroup_dailies:type_name -> moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PlaygroupDailyView
	7,  // 5: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.pets:type_name -> moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PetView
	8,  // 6: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.appointments:type_name -> moego.api.appointment.v1.ListPlaygroupCalendarViewResult.AppointmentView
	13, // 7: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.playgroup_pagination:type_name -> moego.utils.v2.PaginationResponse
	10, // 8: moego.api.appointment.v1.ReschedulePetPlaygroupParams.date:type_name -> google.type.Date
	10, // 9: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PlaygroupDailyView.date:type_name -> google.type.Date
	5,  // 10: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PlaygroupDailyView.playgroups:type_name -> moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PlaygroupView
	6,  // 11: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PlaygroupView.pet_playgroups:type_name -> moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PetPlaygroupView
	14, // 12: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PetView.pet_type:type_name -> moego.models.customer.v1.PetType
	15, // 13: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PetView.vaccines:type_name -> moego.api.appointment.v1.VaccineComposite
	9,  // 14: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.PetView.customer:type_name -> moego.api.appointment.v1.ListPlaygroupCalendarViewResult.CustomerView
	16, // 15: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.AppointmentView.appointment_status:type_name -> moego.models.appointment.v1.AppointmentStatus
	17, // 16: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.AppointmentView.service_item_types:type_name -> moego.models.offering.v1.ServiceItemType
	10, // 17: moego.api.appointment.v1.ListPlaygroupCalendarViewResult.AppointmentView.appointment_start_date:type_name -> google.type.Date
	0,  // 18: moego.api.appointment.v1.PetPlaygroupService.ListPlaygroupCalendarView:input_type -> moego.api.appointment.v1.ListPlaygroupCalendarViewParams
	2,  // 19: moego.api.appointment.v1.PetPlaygroupService.ReschedulePetPlaygroup:input_type -> moego.api.appointment.v1.ReschedulePetPlaygroupParams
	1,  // 20: moego.api.appointment.v1.PetPlaygroupService.ListPlaygroupCalendarView:output_type -> moego.api.appointment.v1.ListPlaygroupCalendarViewResult
	3,  // 21: moego.api.appointment.v1.PetPlaygroupService.ReschedulePetPlaygroup:output_type -> moego.api.appointment.v1.ReschedulePetPlaygroupResult
	20, // [20:22] is the sub-list for method output_type
	18, // [18:20] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_moego_api_appointment_v1_pet_playgroup_api_proto_init() }
func file_moego_api_appointment_v1_pet_playgroup_api_proto_init() {
	if File_moego_api_appointment_v1_pet_playgroup_api_proto != nil {
		return
	}
	file_moego_api_appointment_v1_overview_api_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupCalendarViewParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupCalendarViewResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReschedulePetPlaygroupParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReschedulePetPlaygroupResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupCalendarViewResult_PlaygroupDailyView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupCalendarViewResult_PlaygroupView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupCalendarViewResult_PetPlaygroupView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupCalendarViewResult_PetView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupCalendarViewResult_AppointmentView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPlaygroupCalendarViewResult_CustomerView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_appointment_v1_pet_playgroup_api_proto_goTypes,
		DependencyIndexes: file_moego_api_appointment_v1_pet_playgroup_api_proto_depIdxs,
		MessageInfos:      file_moego_api_appointment_v1_pet_playgroup_api_proto_msgTypes,
	}.Build()
	File_moego_api_appointment_v1_pet_playgroup_api_proto = out.File
	file_moego_api_appointment_v1_pet_playgroup_api_proto_rawDesc = nil
	file_moego_api_appointment_v1_pet_playgroup_api_proto_goTypes = nil
	file_moego_api_appointment_v1_pet_playgroup_api_proto_depIdxs = nil
}
