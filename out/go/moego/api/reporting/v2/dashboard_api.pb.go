// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/reporting/v2/dashboard_api.proto

package reportingapipb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	interval "google.golang.org/genproto/googleapis/type/interval"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Describe pages of dashboard
type QueryDashboardPagesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The tabs of query dashboard page
	Tabs []v2.DashboardPage_Tab `protobuf:"varint,1,rep,packed,name=tabs,proto3,enum=moego.models.reporting.v2.DashboardPage_Tab" json:"tabs,omitempty"`
	// 区分不同的 reporting 场景
	ReportingType *v2.ReportingScene `protobuf:"varint,2,opt,name=reporting_type,json=reportingType,proto3,enum=moego.models.reporting.v2.ReportingScene,oneof" json:"reporting_type,omitempty"`
}

func (x *QueryDashboardPagesRequest) Reset() {
	*x = QueryDashboardPagesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDashboardPagesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDashboardPagesRequest) ProtoMessage() {}

func (x *QueryDashboardPagesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDashboardPagesRequest.ProtoReflect.Descriptor instead.
func (*QueryDashboardPagesRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_dashboard_api_proto_rawDescGZIP(), []int{0}
}

func (x *QueryDashboardPagesRequest) GetTabs() []v2.DashboardPage_Tab {
	if x != nil {
		return x.Tabs
	}
	return nil
}

func (x *QueryDashboardPagesRequest) GetReportingType() v2.ReportingScene {
	if x != nil && x.ReportingType != nil {
		return *x.ReportingType
	}
	return v2.ReportingScene(0)
}

// Describe pages of dashboard
type QueryDashboardPagesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of dashboard pages
	DashboardPages []*v2.DashboardPage `protobuf:"bytes,1,rep,name=dashboard_pages,json=dashboardPages,proto3" json:"dashboard_pages,omitempty"`
}

func (x *QueryDashboardPagesResponse) Reset() {
	*x = QueryDashboardPagesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryDashboardPagesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryDashboardPagesResponse) ProtoMessage() {}

func (x *QueryDashboardPagesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryDashboardPagesResponse.ProtoReflect.Descriptor instead.
func (*QueryDashboardPagesResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_dashboard_api_proto_rawDescGZIP(), []int{1}
}

func (x *QueryDashboardPagesResponse) GetDashboardPages() []*v2.DashboardPage {
	if x != nil {
		return x.DashboardPages
	}
	return nil
}

// Describe a request to fetch dashboard diagram data
type FetchDashboardDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram ids
	DiagramIds []string `protobuf:"bytes,1,rep,name=diagram_ids,json=diagramIds,proto3" json:"diagram_ids,omitempty"`
	// The business id
	BusinessIds []uint64 `protobuf:"varint,2,rep,packed,name=business_ids,json=businessIds,proto3" json:"business_ids,omitempty"`
	// current interval
	CurrentPeriod *interval.Interval `protobuf:"bytes,4,opt,name=current_period,json=currentPeriod,proto3" json:"current_period,omitempty"`
	// previous interval
	PreviousPeriod *interval.Interval `protobuf:"bytes,5,opt,name=previous_period,json=previousPeriod,proto3,oneof" json:"previous_period,omitempty"`
	// group by field key
	GroupByFieldKeys []string `protobuf:"bytes,6,rep,name=group_by_field_keys,json=groupByFieldKeys,proto3" json:"group_by_field_keys,omitempty"`
	// Filters
	Filters []*v2.FilterRequest `protobuf:"bytes,7,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *FetchDashboardDataRequest) Reset() {
	*x = FetchDashboardDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDashboardDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDashboardDataRequest) ProtoMessage() {}

func (x *FetchDashboardDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDashboardDataRequest.ProtoReflect.Descriptor instead.
func (*FetchDashboardDataRequest) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_dashboard_api_proto_rawDescGZIP(), []int{2}
}

func (x *FetchDashboardDataRequest) GetDiagramIds() []string {
	if x != nil {
		return x.DiagramIds
	}
	return nil
}

func (x *FetchDashboardDataRequest) GetBusinessIds() []uint64 {
	if x != nil {
		return x.BusinessIds
	}
	return nil
}

func (x *FetchDashboardDataRequest) GetCurrentPeriod() *interval.Interval {
	if x != nil {
		return x.CurrentPeriod
	}
	return nil
}

func (x *FetchDashboardDataRequest) GetPreviousPeriod() *interval.Interval {
	if x != nil {
		return x.PreviousPeriod
	}
	return nil
}

func (x *FetchDashboardDataRequest) GetGroupByFieldKeys() []string {
	if x != nil {
		return x.GroupByFieldKeys
	}
	return nil
}

func (x *FetchDashboardDataRequest) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

// Describe a response to fetch dashboard diagram data
type FetchDashboardDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The dashboard diagram data
	DiagramData []*v2.DiagramData `protobuf:"bytes,1,rep,name=diagram_data,json=diagramData,proto3" json:"diagram_data,omitempty"`
	// Report data last synced time
	LastSyncedTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_synced_time,json=lastSyncedTime,proto3" json:"last_synced_time,omitempty"`
}

func (x *FetchDashboardDataResponse) Reset() {
	*x = FetchDashboardDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchDashboardDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchDashboardDataResponse) ProtoMessage() {}

func (x *FetchDashboardDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchDashboardDataResponse.ProtoReflect.Descriptor instead.
func (*FetchDashboardDataResponse) Descriptor() ([]byte, []int) {
	return file_moego_api_reporting_v2_dashboard_api_proto_rawDescGZIP(), []int{3}
}

func (x *FetchDashboardDataResponse) GetDiagramData() []*v2.DiagramData {
	if x != nil {
		return x.DiagramData
	}
	return nil
}

func (x *FetchDashboardDataResponse) GetLastSyncedTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSyncedTime
	}
	return nil
}

var File_moego_api_reporting_v2_dashboard_api_proto protoreflect.FileDescriptor

var file_moego_api_reporting_v2_dashboard_api_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x64, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe7, 0x01, 0x0a, 0x1a, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x04, 0x74, 0x61, 0x62, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x2e,
	0x54, 0x61, 0x62, 0x42, 0x11, 0xfa, 0x42, 0x0e, 0x92, 0x01, 0x0b, 0x18, 0x01, 0x22, 0x07, 0x82,
	0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x04, 0x74, 0x61, 0x62, 0x73, 0x12, 0x61, 0x0a, 0x0e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x00, 0x52, 0x0d, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42,
	0x11, 0x0a, 0x0f, 0x5f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x22, 0x70, 0x0a, 0x1b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x51, 0x0a, 0x0f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x0e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50,
	0x61, 0x67, 0x65, 0x73, 0x22, 0x93, 0x03, 0x0a, 0x19, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01, 0x08, 0x22,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d,
	0x49, 0x64, 0x73, 0x12, 0x31, 0x0a, 0x0c, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x42, 0x0e, 0xfa, 0x42, 0x0b, 0x92, 0x01,
	0x08, 0x18, 0x01, 0x22, 0x04, 0x32, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x49, 0x64, 0x73, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x43,
	0x0a, 0x0f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x48, 0x00,
	0x52, 0x0e, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x13, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x62, 0x79, 0x5f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x10, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65,
	0x79, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69,
	0x6f, 0x75, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x1a, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x0c, 0x64, 0x69, 0x61,
	0x67, 0x72, 0x61, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x61, 0x67,
	0x72, 0x61, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x79, 0x6e,
	0x63, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74,
	0x53, 0x79, 0x6e, 0x63, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x32, 0x8f, 0x02, 0x0a, 0x10, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x7e, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x50, 0x61, 0x67, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x50, 0x61,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x50, 0x61, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x7b, 0x0a, 0x12, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x7e, 0x0a, 0x1e,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x50, 0x01,
	0x5a, 0x5a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65,
	0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d,
	0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_reporting_v2_dashboard_api_proto_rawDescOnce sync.Once
	file_moego_api_reporting_v2_dashboard_api_proto_rawDescData = file_moego_api_reporting_v2_dashboard_api_proto_rawDesc
)

func file_moego_api_reporting_v2_dashboard_api_proto_rawDescGZIP() []byte {
	file_moego_api_reporting_v2_dashboard_api_proto_rawDescOnce.Do(func() {
		file_moego_api_reporting_v2_dashboard_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_reporting_v2_dashboard_api_proto_rawDescData)
	})
	return file_moego_api_reporting_v2_dashboard_api_proto_rawDescData
}

var file_moego_api_reporting_v2_dashboard_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_api_reporting_v2_dashboard_api_proto_goTypes = []interface{}{
	(*QueryDashboardPagesRequest)(nil),  // 0: moego.api.reporting.v2.QueryDashboardPagesRequest
	(*QueryDashboardPagesResponse)(nil), // 1: moego.api.reporting.v2.QueryDashboardPagesResponse
	(*FetchDashboardDataRequest)(nil),   // 2: moego.api.reporting.v2.FetchDashboardDataRequest
	(*FetchDashboardDataResponse)(nil),  // 3: moego.api.reporting.v2.FetchDashboardDataResponse
	(v2.DashboardPage_Tab)(0),           // 4: moego.models.reporting.v2.DashboardPage.Tab
	(v2.ReportingScene)(0),              // 5: moego.models.reporting.v2.ReportingScene
	(*v2.DashboardPage)(nil),            // 6: moego.models.reporting.v2.DashboardPage
	(*interval.Interval)(nil),           // 7: google.type.Interval
	(*v2.FilterRequest)(nil),            // 8: moego.models.reporting.v2.FilterRequest
	(*v2.DiagramData)(nil),              // 9: moego.models.reporting.v2.DiagramData
	(*timestamppb.Timestamp)(nil),       // 10: google.protobuf.Timestamp
}
var file_moego_api_reporting_v2_dashboard_api_proto_depIdxs = []int32{
	4,  // 0: moego.api.reporting.v2.QueryDashboardPagesRequest.tabs:type_name -> moego.models.reporting.v2.DashboardPage.Tab
	5,  // 1: moego.api.reporting.v2.QueryDashboardPagesRequest.reporting_type:type_name -> moego.models.reporting.v2.ReportingScene
	6,  // 2: moego.api.reporting.v2.QueryDashboardPagesResponse.dashboard_pages:type_name -> moego.models.reporting.v2.DashboardPage
	7,  // 3: moego.api.reporting.v2.FetchDashboardDataRequest.current_period:type_name -> google.type.Interval
	7,  // 4: moego.api.reporting.v2.FetchDashboardDataRequest.previous_period:type_name -> google.type.Interval
	8,  // 5: moego.api.reporting.v2.FetchDashboardDataRequest.filters:type_name -> moego.models.reporting.v2.FilterRequest
	9,  // 6: moego.api.reporting.v2.FetchDashboardDataResponse.diagram_data:type_name -> moego.models.reporting.v2.DiagramData
	10, // 7: moego.api.reporting.v2.FetchDashboardDataResponse.last_synced_time:type_name -> google.protobuf.Timestamp
	0,  // 8: moego.api.reporting.v2.DashboardService.QueryDashboardPages:input_type -> moego.api.reporting.v2.QueryDashboardPagesRequest
	2,  // 9: moego.api.reporting.v2.DashboardService.FetchDashboardData:input_type -> moego.api.reporting.v2.FetchDashboardDataRequest
	1,  // 10: moego.api.reporting.v2.DashboardService.QueryDashboardPages:output_type -> moego.api.reporting.v2.QueryDashboardPagesResponse
	3,  // 11: moego.api.reporting.v2.DashboardService.FetchDashboardData:output_type -> moego.api.reporting.v2.FetchDashboardDataResponse
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_moego_api_reporting_v2_dashboard_api_proto_init() }
func file_moego_api_reporting_v2_dashboard_api_proto_init() {
	if File_moego_api_reporting_v2_dashboard_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDashboardPagesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryDashboardPagesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDashboardDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchDashboardDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_api_reporting_v2_dashboard_api_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_reporting_v2_dashboard_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_reporting_v2_dashboard_api_proto_goTypes,
		DependencyIndexes: file_moego_api_reporting_v2_dashboard_api_proto_depIdxs,
		MessageInfos:      file_moego_api_reporting_v2_dashboard_api_proto_msgTypes,
	}.Build()
	File_moego_api_reporting_v2_dashboard_api_proto = out.File
	file_moego_api_reporting_v2_dashboard_api_proto_rawDesc = nil
	file_moego_api_reporting_v2_dashboard_api_proto_goTypes = nil
	file_moego_api_reporting_v2_dashboard_api_proto_depIdxs = nil
}
