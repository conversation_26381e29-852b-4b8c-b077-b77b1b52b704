// @since 2024-07-10 14:37:32
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/api/branded_app/v1/branded_app_api.proto

package brandedAppApiV1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The params for GetBrandedAppConfig
type GetBrandedAppConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetBrandedAppConfigParams) Reset() {
	*x = GetBrandedAppConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBrandedAppConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBrandedAppConfigParams) ProtoMessage() {}

func (x *GetBrandedAppConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBrandedAppConfigParams.ProtoReflect.Descriptor instead.
func (*GetBrandedAppConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_api_branded_app_v1_branded_app_api_proto_rawDescGZIP(), []int{0}
}

// The result for GetBrandedAppConfig
type GetBrandedAppConfigResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// the branded_app config
	Config *BrandedAppConfigView `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *GetBrandedAppConfigResult) Reset() {
	*x = GetBrandedAppConfigResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBrandedAppConfigResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBrandedAppConfigResult) ProtoMessage() {}

func (x *GetBrandedAppConfigResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBrandedAppConfigResult.ProtoReflect.Descriptor instead.
func (*GetBrandedAppConfigResult) Descriptor() ([]byte, []int) {
	return file_moego_api_branded_app_v1_branded_app_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetBrandedAppConfigResult) GetConfig() *BrandedAppConfigView {
	if x != nil {
		return x.Config
	}
	return nil
}

// Branded app config view
type BrandedAppConfigView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// branded app id
	BrandedAppId string `protobuf:"bytes,1,opt,name=branded_app_id,json=brandedAppId,proto3" json:"branded_app_id,omitempty"`
	// App icon url
	AppIconUrl string `protobuf:"bytes,2,opt,name=app_icon_url,json=appIconUrl,proto3" json:"app_icon_url,omitempty"`
	// app name
	AppName string `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
}

func (x *BrandedAppConfigView) Reset() {
	*x = BrandedAppConfigView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BrandedAppConfigView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrandedAppConfigView) ProtoMessage() {}

func (x *BrandedAppConfigView) ProtoReflect() protoreflect.Message {
	mi := &file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrandedAppConfigView.ProtoReflect.Descriptor instead.
func (*BrandedAppConfigView) Descriptor() ([]byte, []int) {
	return file_moego_api_branded_app_v1_branded_app_api_proto_rawDescGZIP(), []int{2}
}

func (x *BrandedAppConfigView) GetBrandedAppId() string {
	if x != nil {
		return x.BrandedAppId
	}
	return ""
}

func (x *BrandedAppConfigView) GetAppIconUrl() string {
	if x != nil {
		return x.AppIconUrl
	}
	return ""
}

func (x *BrandedAppConfigView) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

var File_moego_api_branded_app_v1_branded_app_api_proto protoreflect.FileDescriptor

var file_moego_api_branded_app_v1_branded_app_api_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x72, 0x61, 0x6e, 0x64,
	0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x18, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x72, 0x61, 0x6e,
	0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x22, 0x1b, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x63, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x42, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x79, 0x0a, 0x14,
	0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x72,
	0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x70, 0x70, 0x49, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x32, 0x94, 0x01, 0x0a, 0x11, 0x42, 0x72, 0x61, 0x6e,
	0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7f, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41,
	0x70, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x83,
	0x01, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70,
	0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x5d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x70,
	0x70, 0x2f, 0x76, 0x31, 0x3b, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x70, 0x70, 0x41,
	0x70, 0x69, 0x56, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_api_branded_app_v1_branded_app_api_proto_rawDescOnce sync.Once
	file_moego_api_branded_app_v1_branded_app_api_proto_rawDescData = file_moego_api_branded_app_v1_branded_app_api_proto_rawDesc
)

func file_moego_api_branded_app_v1_branded_app_api_proto_rawDescGZIP() []byte {
	file_moego_api_branded_app_v1_branded_app_api_proto_rawDescOnce.Do(func() {
		file_moego_api_branded_app_v1_branded_app_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_api_branded_app_v1_branded_app_api_proto_rawDescData)
	})
	return file_moego_api_branded_app_v1_branded_app_api_proto_rawDescData
}

var file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_api_branded_app_v1_branded_app_api_proto_goTypes = []interface{}{
	(*GetBrandedAppConfigParams)(nil), // 0: moego.api.branded_app.v1.GetBrandedAppConfigParams
	(*GetBrandedAppConfigResult)(nil), // 1: moego.api.branded_app.v1.GetBrandedAppConfigResult
	(*BrandedAppConfigView)(nil),      // 2: moego.api.branded_app.v1.BrandedAppConfigView
}
var file_moego_api_branded_app_v1_branded_app_api_proto_depIdxs = []int32{
	2, // 0: moego.api.branded_app.v1.GetBrandedAppConfigResult.config:type_name -> moego.api.branded_app.v1.BrandedAppConfigView
	0, // 1: moego.api.branded_app.v1.BrandedAppService.GetBrandedAppConfig:input_type -> moego.api.branded_app.v1.GetBrandedAppConfigParams
	1, // 2: moego.api.branded_app.v1.BrandedAppService.GetBrandedAppConfig:output_type -> moego.api.branded_app.v1.GetBrandedAppConfigResult
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_api_branded_app_v1_branded_app_api_proto_init() }
func file_moego_api_branded_app_v1_branded_app_api_proto_init() {
	if File_moego_api_branded_app_v1_branded_app_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBrandedAppConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBrandedAppConfigResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BrandedAppConfigView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_api_branded_app_v1_branded_app_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_api_branded_app_v1_branded_app_api_proto_goTypes,
		DependencyIndexes: file_moego_api_branded_app_v1_branded_app_api_proto_depIdxs,
		MessageInfos:      file_moego_api_branded_app_v1_branded_app_api_proto_msgTypes,
	}.Build()
	File_moego_api_branded_app_v1_branded_app_api_proto = out.File
	file_moego_api_branded_app_v1_branded_app_api_proto_rawDesc = nil
	file_moego_api_branded_app_v1_branded_app_api_proto_goTypes = nil
	file_moego_api_branded_app_v1_branded_app_api_proto_depIdxs = nil
}
