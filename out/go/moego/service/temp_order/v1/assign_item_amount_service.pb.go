// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/temp_order/v1/assign_item_amount_service.proto

package ordersvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request for assigning paid amount to specific items
type AssignItemPaidAmountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// List of items to assign paid amount
	Items []*v1.ItemPaidAmountAssignment `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *AssignItemPaidAmountRequest) Reset() {
	*x = AssignItemPaidAmountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignItemPaidAmountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignItemPaidAmountRequest) ProtoMessage() {}

func (x *AssignItemPaidAmountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignItemPaidAmountRequest.ProtoReflect.Descriptor instead.
func (*AssignItemPaidAmountRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescGZIP(), []int{0}
}

func (x *AssignItemPaidAmountRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *AssignItemPaidAmountRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *AssignItemPaidAmountRequest) GetItems() []*v1.ItemPaidAmountAssignment {
	if x != nil {
		return x.Items
	}
	return nil
}

// Response for assigning paid amount to specific items
type AssignItemPaidAmountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Whether the operation was successful
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *AssignItemPaidAmountResponse) Reset() {
	*x = AssignItemPaidAmountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssignItemPaidAmountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignItemPaidAmountResponse) ProtoMessage() {}

func (x *AssignItemPaidAmountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignItemPaidAmountResponse.ProtoReflect.Descriptor instead.
func (*AssignItemPaidAmountResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescGZIP(), []int{1}
}

func (x *AssignItemPaidAmountResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Request for getting assigned paid amounts for order items
type GetAssignedItemPaidAmountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// Business ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
}

func (x *GetAssignedItemPaidAmountRequest) Reset() {
	*x = GetAssignedItemPaidAmountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssignedItemPaidAmountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssignedItemPaidAmountRequest) ProtoMessage() {}

func (x *GetAssignedItemPaidAmountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssignedItemPaidAmountRequest.ProtoReflect.Descriptor instead.
func (*GetAssignedItemPaidAmountRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetAssignedItemPaidAmountRequest) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *GetAssignedItemPaidAmountRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

// Response for getting assigned paid amounts for order items
type GetAssignedItemPaidAmountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order ID
	OrderId int64 `protobuf:"varint,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// List of item payment assignments
	Items []*v1.ItemPaidAmountAssignment `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetAssignedItemPaidAmountResponse) Reset() {
	*x = GetAssignedItemPaidAmountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssignedItemPaidAmountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssignedItemPaidAmountResponse) ProtoMessage() {}

func (x *GetAssignedItemPaidAmountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssignedItemPaidAmountResponse.ProtoReflect.Descriptor instead.
func (*GetAssignedItemPaidAmountResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetAssignedItemPaidAmountResponse) GetOrderId() int64 {
	if x != nil {
		return x.OrderId
	}
	return 0
}

func (x *GetAssignedItemPaidAmountResponse) GetItems() []*v1.ItemPaidAmountAssignment {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_moego_service_temp_order_v1_assign_item_amount_service_proto protoreflect.FileDescriptor

var file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x74, 0x65, 0x6d, 0x70, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1b,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x65,
	0x6d, 0x70, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x3e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f,
	0x76, 0x31, 0x2f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbe, 0x01, 0x0a, 0x1b, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69,
	0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x49, 0x64, 0x12, 0x51, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61,
	0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x14, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x38, 0x0a, 0x1c, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22,
	0x70, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49,
	0x64, 0x22, 0x8e, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x32, 0xc4, 0x02, 0x0a, 0x17, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8b,
	0x01, 0x0a, 0x14, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69,
	0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x74, 0x65, 0x6d,
	0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9a, 0x01, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x65, 0x6d, 0x70, 0x5f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x50, 0x61, 0x69, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x84, 0x01, 0x0a, 0x23, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x5b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x5f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x76, 0x63, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescOnce sync.Once
	file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescData = file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDesc
)

func file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescGZIP() []byte {
	file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescOnce.Do(func() {
		file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescData)
	})
	return file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDescData
}

var file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_temp_order_v1_assign_item_amount_service_proto_goTypes = []interface{}{
	(*AssignItemPaidAmountRequest)(nil),       // 0: moego.service.temp_order.v1.AssignItemPaidAmountRequest
	(*AssignItemPaidAmountResponse)(nil),      // 1: moego.service.temp_order.v1.AssignItemPaidAmountResponse
	(*GetAssignedItemPaidAmountRequest)(nil),  // 2: moego.service.temp_order.v1.GetAssignedItemPaidAmountRequest
	(*GetAssignedItemPaidAmountResponse)(nil), // 3: moego.service.temp_order.v1.GetAssignedItemPaidAmountResponse
	(*v1.ItemPaidAmountAssignment)(nil),       // 4: moego.models.order.v1.ItemPaidAmountAssignment
}
var file_moego_service_temp_order_v1_assign_item_amount_service_proto_depIdxs = []int32{
	4, // 0: moego.service.temp_order.v1.AssignItemPaidAmountRequest.items:type_name -> moego.models.order.v1.ItemPaidAmountAssignment
	4, // 1: moego.service.temp_order.v1.GetAssignedItemPaidAmountResponse.items:type_name -> moego.models.order.v1.ItemPaidAmountAssignment
	0, // 2: moego.service.temp_order.v1.AssignItemAmountService.AssignItemPaidAmount:input_type -> moego.service.temp_order.v1.AssignItemPaidAmountRequest
	2, // 3: moego.service.temp_order.v1.AssignItemAmountService.GetAssignedItemPaidAmount:input_type -> moego.service.temp_order.v1.GetAssignedItemPaidAmountRequest
	1, // 4: moego.service.temp_order.v1.AssignItemAmountService.AssignItemPaidAmount:output_type -> moego.service.temp_order.v1.AssignItemPaidAmountResponse
	3, // 5: moego.service.temp_order.v1.AssignItemAmountService.GetAssignedItemPaidAmount:output_type -> moego.service.temp_order.v1.GetAssignedItemPaidAmountResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_moego_service_temp_order_v1_assign_item_amount_service_proto_init() }
func file_moego_service_temp_order_v1_assign_item_amount_service_proto_init() {
	if File_moego_service_temp_order_v1_assign_item_amount_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssignItemPaidAmountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssignItemPaidAmountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssignedItemPaidAmountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssignedItemPaidAmountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_temp_order_v1_assign_item_amount_service_proto_goTypes,
		DependencyIndexes: file_moego_service_temp_order_v1_assign_item_amount_service_proto_depIdxs,
		MessageInfos:      file_moego_service_temp_order_v1_assign_item_amount_service_proto_msgTypes,
	}.Build()
	File_moego_service_temp_order_v1_assign_item_amount_service_proto = out.File
	file_moego_service_temp_order_v1_assign_item_amount_service_proto_rawDesc = nil
	file_moego_service_temp_order_v1_assign_item_amount_service_proto_goTypes = nil
	file_moego_service_temp_order_v1_assign_item_amount_service_proto_depIdxs = nil
}
