// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/reporting/v2/attribute_service.proto

package reportingsvcpb

import (
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Get dimensions request
type GetDimensionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id to get dimensions, if not present, return all dimensions
	DiagramId *string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3,oneof" json:"diagram_id,omitempty"`
	// reporting scene
	Scene v2.ReportingScene `protobuf:"varint,2,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
}

func (x *GetDimensionsRequest) Reset() {
	*x = GetDimensionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_attribute_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDimensionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDimensionsRequest) ProtoMessage() {}

func (x *GetDimensionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_attribute_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDimensionsRequest.ProtoReflect.Descriptor instead.
func (*GetDimensionsRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_attribute_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetDimensionsRequest) GetDiagramId() string {
	if x != nil && x.DiagramId != nil {
		return *x.DiagramId
	}
	return ""
}

func (x *GetDimensionsRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

// Get dimensions response
type GetDimensionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// dimensions
	Dimensions []*v2.DimensionField `protobuf:"bytes,1,rep,name=dimensions,proto3" json:"dimensions,omitempty"`
}

func (x *GetDimensionsResponse) Reset() {
	*x = GetDimensionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_attribute_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDimensionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDimensionsResponse) ProtoMessage() {}

func (x *GetDimensionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_attribute_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDimensionsResponse.ProtoReflect.Descriptor instead.
func (*GetDimensionsResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_attribute_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetDimensionsResponse) GetDimensions() []*v2.DimensionField {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

// Get metrics categories request
type GetMetricsCategoriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// diagram id to get metrics, if not present, return all metrics
	DiagramId *string `protobuf:"bytes,1,opt,name=diagram_id,json=diagramId,proto3,oneof" json:"diagram_id,omitempty"`
	// reporting scene
	Scene v2.ReportingScene `protobuf:"varint,2,opt,name=scene,proto3,enum=moego.models.reporting.v2.ReportingScene" json:"scene,omitempty"`
}

func (x *GetMetricsCategoriesRequest) Reset() {
	*x = GetMetricsCategoriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_attribute_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMetricsCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMetricsCategoriesRequest) ProtoMessage() {}

func (x *GetMetricsCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_attribute_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMetricsCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetMetricsCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_attribute_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetMetricsCategoriesRequest) GetDiagramId() string {
	if x != nil && x.DiagramId != nil {
		return *x.DiagramId
	}
	return ""
}

func (x *GetMetricsCategoriesRequest) GetScene() v2.ReportingScene {
	if x != nil {
		return x.Scene
	}
	return v2.ReportingScene(0)
}

// Get metrics categories response
type GetMetricsCategoriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// metrics categories
	Categories []*v2.MetricsCategoryDef `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *GetMetricsCategoriesResponse) Reset() {
	*x = GetMetricsCategoriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_reporting_v2_attribute_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMetricsCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMetricsCategoriesResponse) ProtoMessage() {}

func (x *GetMetricsCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_reporting_v2_attribute_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMetricsCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetMetricsCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_reporting_v2_attribute_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetMetricsCategoriesResponse) GetCategories() []*v2.MetricsCategoryDef {
	if x != nil {
		return x.Categories
	}
	return nil
}

var File_moego_service_reporting_v2_attribute_service_proto protoreflect.FileDescriptor

var file_moego_service_reporting_v2_attribute_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x74, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x1a, 0x2d, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x61, 0x74, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x5f, 0x64, 0x65, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72,
	0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x64,
	0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x05, 0x73,
	0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x22, 0x62, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x52, 0x0a, 0x64, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22,
	0x91, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x0a, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x05, 0x73,
	0x63, 0x65, 0x6e, 0x65, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x69, 0x61, 0x67, 0x72, 0x61, 0x6d,
	0x5f, 0x69, 0x64, 0x22, 0x6d, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x32, 0x94, 0x02, 0x0a, 0x10, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x74, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x44, 0x69,
	0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x6d, 0x65, 0x6e,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x86, 0x01, 0x0a, 0x22, 0x63, 0x6f,
	0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x50, 0x01, 0x5a, 0x5e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x32, 0x3b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x76, 0x63,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_reporting_v2_attribute_service_proto_rawDescOnce sync.Once
	file_moego_service_reporting_v2_attribute_service_proto_rawDescData = file_moego_service_reporting_v2_attribute_service_proto_rawDesc
)

func file_moego_service_reporting_v2_attribute_service_proto_rawDescGZIP() []byte {
	file_moego_service_reporting_v2_attribute_service_proto_rawDescOnce.Do(func() {
		file_moego_service_reporting_v2_attribute_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_reporting_v2_attribute_service_proto_rawDescData)
	})
	return file_moego_service_reporting_v2_attribute_service_proto_rawDescData
}

var file_moego_service_reporting_v2_attribute_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_service_reporting_v2_attribute_service_proto_goTypes = []interface{}{
	(*GetDimensionsRequest)(nil),         // 0: moego.service.reporting.v2.GetDimensionsRequest
	(*GetDimensionsResponse)(nil),        // 1: moego.service.reporting.v2.GetDimensionsResponse
	(*GetMetricsCategoriesRequest)(nil),  // 2: moego.service.reporting.v2.GetMetricsCategoriesRequest
	(*GetMetricsCategoriesResponse)(nil), // 3: moego.service.reporting.v2.GetMetricsCategoriesResponse
	(v2.ReportingScene)(0),               // 4: moego.models.reporting.v2.ReportingScene
	(*v2.DimensionField)(nil),            // 5: moego.models.reporting.v2.DimensionField
	(*v2.MetricsCategoryDef)(nil),        // 6: moego.models.reporting.v2.MetricsCategoryDef
}
var file_moego_service_reporting_v2_attribute_service_proto_depIdxs = []int32{
	4, // 0: moego.service.reporting.v2.GetDimensionsRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	5, // 1: moego.service.reporting.v2.GetDimensionsResponse.dimensions:type_name -> moego.models.reporting.v2.DimensionField
	4, // 2: moego.service.reporting.v2.GetMetricsCategoriesRequest.scene:type_name -> moego.models.reporting.v2.ReportingScene
	6, // 3: moego.service.reporting.v2.GetMetricsCategoriesResponse.categories:type_name -> moego.models.reporting.v2.MetricsCategoryDef
	0, // 4: moego.service.reporting.v2.AttributeService.GetDimensions:input_type -> moego.service.reporting.v2.GetDimensionsRequest
	2, // 5: moego.service.reporting.v2.AttributeService.GetMetricsCategories:input_type -> moego.service.reporting.v2.GetMetricsCategoriesRequest
	1, // 6: moego.service.reporting.v2.AttributeService.GetDimensions:output_type -> moego.service.reporting.v2.GetDimensionsResponse
	3, // 7: moego.service.reporting.v2.AttributeService.GetMetricsCategories:output_type -> moego.service.reporting.v2.GetMetricsCategoriesResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_moego_service_reporting_v2_attribute_service_proto_init() }
func file_moego_service_reporting_v2_attribute_service_proto_init() {
	if File_moego_service_reporting_v2_attribute_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_reporting_v2_attribute_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDimensionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_attribute_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDimensionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_attribute_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMetricsCategoriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_reporting_v2_attribute_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMetricsCategoriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_service_reporting_v2_attribute_service_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_service_reporting_v2_attribute_service_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_reporting_v2_attribute_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_reporting_v2_attribute_service_proto_goTypes,
		DependencyIndexes: file_moego_service_reporting_v2_attribute_service_proto_depIdxs,
		MessageInfos:      file_moego_service_reporting_v2_attribute_service_proto_msgTypes,
	}.Build()
	File_moego_service_reporting_v2_attribute_service_proto = out.File
	file_moego_service_reporting_v2_attribute_service_proto_rawDesc = nil
	file_moego_service_reporting_v2_attribute_service_proto_goTypes = nil
	file_moego_service_reporting_v2_attribute_service_proto_depIdxs = nil
}
