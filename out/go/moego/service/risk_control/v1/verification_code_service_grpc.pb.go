// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/risk_control/v1/verification_code_service.proto

package riskcontrolsvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// VerificationCodeServiceClient is the client API for VerificationCodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VerificationCodeServiceClient interface {
	// Create verification code
	Create(ctx context.Context, in *CreateCodeRequest, opts ...grpc.CallOption) (*CreateCodeResponse, error)
	// Check verification code and destroy it
	Verify(ctx context.Context, in *VerifyCodeRequest, opts ...grpc.CallOption) (*VerifyCodeResponse, error)
	// Check whether the verification code are consistent
	Check(ctx context.Context, in *VerifyCodeRequest, opts ...grpc.CallOption) (*VerifyCodeResponse, error)
}

type verificationCodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVerificationCodeServiceClient(cc grpc.ClientConnInterface) VerificationCodeServiceClient {
	return &verificationCodeServiceClient{cc}
}

func (c *verificationCodeServiceClient) Create(ctx context.Context, in *CreateCodeRequest, opts ...grpc.CallOption) (*CreateCodeResponse, error) {
	out := new(CreateCodeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.risk_control.v1.VerificationCodeService/Create", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *verificationCodeServiceClient) Verify(ctx context.Context, in *VerifyCodeRequest, opts ...grpc.CallOption) (*VerifyCodeResponse, error) {
	out := new(VerifyCodeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.risk_control.v1.VerificationCodeService/Verify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *verificationCodeServiceClient) Check(ctx context.Context, in *VerifyCodeRequest, opts ...grpc.CallOption) (*VerifyCodeResponse, error) {
	out := new(VerifyCodeResponse)
	err := c.cc.Invoke(ctx, "/moego.service.risk_control.v1.VerificationCodeService/Check", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VerificationCodeServiceServer is the server API for VerificationCodeService service.
// All implementations must embed UnimplementedVerificationCodeServiceServer
// for forward compatibility
type VerificationCodeServiceServer interface {
	// Create verification code
	Create(context.Context, *CreateCodeRequest) (*CreateCodeResponse, error)
	// Check verification code and destroy it
	Verify(context.Context, *VerifyCodeRequest) (*VerifyCodeResponse, error)
	// Check whether the verification code are consistent
	Check(context.Context, *VerifyCodeRequest) (*VerifyCodeResponse, error)
	mustEmbedUnimplementedVerificationCodeServiceServer()
}

// UnimplementedVerificationCodeServiceServer must be embedded to have forward compatible implementations.
type UnimplementedVerificationCodeServiceServer struct {
}

func (UnimplementedVerificationCodeServiceServer) Create(context.Context, *CreateCodeRequest) (*CreateCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Create not implemented")
}
func (UnimplementedVerificationCodeServiceServer) Verify(context.Context, *VerifyCodeRequest) (*VerifyCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Verify not implemented")
}
func (UnimplementedVerificationCodeServiceServer) Check(context.Context, *VerifyCodeRequest) (*VerifyCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Check not implemented")
}
func (UnimplementedVerificationCodeServiceServer) mustEmbedUnimplementedVerificationCodeServiceServer() {
}

// UnsafeVerificationCodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VerificationCodeServiceServer will
// result in compilation errors.
type UnsafeVerificationCodeServiceServer interface {
	mustEmbedUnimplementedVerificationCodeServiceServer()
}

func RegisterVerificationCodeServiceServer(s grpc.ServiceRegistrar, srv VerificationCodeServiceServer) {
	s.RegisterService(&VerificationCodeService_ServiceDesc, srv)
}

func _VerificationCodeService_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerificationCodeServiceServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.risk_control.v1.VerificationCodeService/Create",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerificationCodeServiceServer).Create(ctx, req.(*CreateCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VerificationCodeService_Verify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerificationCodeServiceServer).Verify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.risk_control.v1.VerificationCodeService/Verify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerificationCodeServiceServer).Verify(ctx, req.(*VerifyCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VerificationCodeService_Check_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VerificationCodeServiceServer).Check(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.risk_control.v1.VerificationCodeService/Check",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VerificationCodeServiceServer).Check(ctx, req.(*VerifyCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VerificationCodeService_ServiceDesc is the grpc.ServiceDesc for VerificationCodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VerificationCodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.risk_control.v1.VerificationCodeService",
	HandlerType: (*VerificationCodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Create",
			Handler:    _VerificationCodeService_Create_Handler,
		},
		{
			MethodName: "Verify",
			Handler:    _VerificationCodeService_Verify_Handler,
		},
		{
			MethodName: "Check",
			Handler:    _VerificationCodeService_Check_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/risk_control/v1/verification_code_service.proto",
}
