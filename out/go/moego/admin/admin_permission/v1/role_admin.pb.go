// @since 2023-05-27 21:28:41
// <AUTHOR> <<EMAIL>>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/admin/admin_permission/v1/role_admin.proto

package adminpermissionapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/admin_permission/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create role req
type CreateRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *CreateRoleParams) Reset() {
	*x = CreateRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoleParams) ProtoMessage() {}

func (x *CreateRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoleParams.ProtoReflect.Descriptor instead.
func (*CreateRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{0}
}

func (x *CreateRoleParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateRoleParams) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// crate role res
type CreateRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role
	Role *v1.RoleModel `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *CreateRoleResult) Reset() {
	*x = CreateRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoleResult) ProtoMessage() {}

func (x *CreateRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoleResult.ProtoReflect.Descriptor instead.
func (*CreateRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{1}
}

func (x *CreateRoleResult) GetRole() *v1.RoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

// get role list
type DescribeRolesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter by ids
	Ids *v11.Int64ListValue `protobuf:"bytes,1,opt,name=ids,proto3,oneof" json:"ids,omitempty"`
	// filter by names
	Names *v11.StringListValue `protobuf:"bytes,2,opt,name=names,proto3,oneof" json:"names,omitempty"`
	// pagination
	Pagination *v2.PaginationRequest `protobuf:"bytes,15,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *DescribeRolesParams) Reset() {
	*x = DescribeRolesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRolesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRolesParams) ProtoMessage() {}

func (x *DescribeRolesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRolesParams.ProtoReflect.Descriptor instead.
func (*DescribeRolesParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{2}
}

func (x *DescribeRolesParams) GetIds() *v11.Int64ListValue {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *DescribeRolesParams) GetNames() *v11.StringListValue {
	if x != nil {
		return x.Names
	}
	return nil
}

func (x *DescribeRolesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get role list res
type DescribeRolesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// roles
	Roles []*v1.RoleModel `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
	// pagination
	Pagination *v2.PaginationResponse `protobuf:"bytes,15,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *DescribeRolesResult) Reset() {
	*x = DescribeRolesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRolesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRolesResult) ProtoMessage() {}

func (x *DescribeRolesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRolesResult.ProtoReflect.Descriptor instead.
func (*DescribeRolesResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{3}
}

func (x *DescribeRolesResult) GetRoles() []*v1.RoleModel {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *DescribeRolesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// get role req
type GetRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// identifier
	//
	// Types that are assignable to Identifier:
	//
	//	*GetRoleParams_Id
	//	*GetRoleParams_Name
	Identifier isGetRoleParams_Identifier `protobuf_oneof:"identifier"`
}

func (x *GetRoleParams) Reset() {
	*x = GetRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleParams) ProtoMessage() {}

func (x *GetRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleParams.ProtoReflect.Descriptor instead.
func (*GetRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{4}
}

func (m *GetRoleParams) GetIdentifier() isGetRoleParams_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetRoleParams) GetId() int64 {
	if x, ok := x.GetIdentifier().(*GetRoleParams_Id); ok {
		return x.Id
	}
	return 0
}

func (x *GetRoleParams) GetName() string {
	if x, ok := x.GetIdentifier().(*GetRoleParams_Name); ok {
		return x.Name
	}
	return ""
}

type isGetRoleParams_Identifier interface {
	isGetRoleParams_Identifier()
}

type GetRoleParams_Id struct {
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3,oneof"`
}

type GetRoleParams_Name struct {
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3,oneof"`
}

func (*GetRoleParams_Id) isGetRoleParams_Identifier() {}

func (*GetRoleParams_Name) isGetRoleParams_Identifier() {}

// get role res
type GetRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role model
	Role *v1.RoleModel `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *GetRoleResult) Reset() {
	*x = GetRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleResult) ProtoMessage() {}

func (x *GetRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleResult.ProtoReflect.Descriptor instead.
func (*GetRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{5}
}

func (x *GetRoleResult) GetRole() *v1.RoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

// update role req
type UpdateRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// description
	Description *string `protobuf:"bytes,3,opt,name=description,proto3,oneof" json:"description,omitempty"`
}

func (x *UpdateRoleParams) Reset() {
	*x = UpdateRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleParams) ProtoMessage() {}

func (x *UpdateRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleParams.ProtoReflect.Descriptor instead.
func (*UpdateRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateRoleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateRoleParams) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateRoleParams) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

// update role res
type UpdateRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role
	Role *v1.RoleModel `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *UpdateRoleResult) Reset() {
	*x = UpdateRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleResult) ProtoMessage() {}

func (x *UpdateRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleResult.ProtoReflect.Descriptor instead.
func (*UpdateRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateRoleResult) GetRole() *v1.RoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

// delete role req
type DeleteRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteRoleParams) Reset() {
	*x = DeleteRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleParams) ProtoMessage() {}

func (x *DeleteRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleParams.ProtoReflect.Descriptor instead.
func (*DeleteRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteRoleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete role res
type DeleteRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteRoleResult) Reset() {
	*x = DeleteRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleResult) ProtoMessage() {}

func (x *DeleteRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleResult.ProtoReflect.Descriptor instead.
func (*DeleteRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{9}
}

// create role permission req
type CreateRolePermissionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id, only allow to create role permission for visible roles
	RoleId int64 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// perm point
	Permission string `protobuf:"bytes,2,opt,name=permission,proto3" json:"permission,omitempty"`
	// filters
	Filters []*v1.FilterDef `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *CreateRolePermissionParams) Reset() {
	*x = CreateRolePermissionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRolePermissionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRolePermissionParams) ProtoMessage() {}

func (x *CreateRolePermissionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRolePermissionParams.ProtoReflect.Descriptor instead.
func (*CreateRolePermissionParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{10}
}

func (x *CreateRolePermissionParams) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *CreateRolePermissionParams) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

func (x *CreateRolePermissionParams) GetFilters() []*v1.FilterDef {
	if x != nil {
		return x.Filters
	}
	return nil
}

// create role perm res
type CreateRolePermissionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// created role permission
	RolePermission *v1.RolePermissionModel `protobuf:"bytes,1,opt,name=role_permission,json=rolePermission,proto3" json:"role_permission,omitempty"`
}

func (x *CreateRolePermissionResult) Reset() {
	*x = CreateRolePermissionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRolePermissionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRolePermissionResult) ProtoMessage() {}

func (x *CreateRolePermissionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRolePermissionResult.ProtoReflect.Descriptor instead.
func (*CreateRolePermissionResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{11}
}

func (x *CreateRolePermissionResult) GetRolePermission() *v1.RolePermissionModel {
	if x != nil {
		return x.RolePermission
	}
	return nil
}

// update role permission req
type UpdateRolePermissionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role permission id
	// only allow to update role permission for visible roles
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// perm point
	Permission string `protobuf:"bytes,2,opt,name=permission,proto3" json:"permission,omitempty"`
	// filters
	Filters []*v1.FilterDef `protobuf:"bytes,3,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *UpdateRolePermissionParams) Reset() {
	*x = UpdateRolePermissionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRolePermissionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRolePermissionParams) ProtoMessage() {}

func (x *UpdateRolePermissionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRolePermissionParams.ProtoReflect.Descriptor instead.
func (*UpdateRolePermissionParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateRolePermissionParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateRolePermissionParams) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

func (x *UpdateRolePermissionParams) GetFilters() []*v1.FilterDef {
	if x != nil {
		return x.Filters
	}
	return nil
}

// update role perm res
type UpdateRolePermissionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// updated role permission
	RolePermission *v1.RolePermissionModel `protobuf:"bytes,1,opt,name=role_permission,json=rolePermission,proto3" json:"role_permission,omitempty"`
}

func (x *UpdateRolePermissionResult) Reset() {
	*x = UpdateRolePermissionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRolePermissionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRolePermissionResult) ProtoMessage() {}

func (x *UpdateRolePermissionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRolePermissionResult.ProtoReflect.Descriptor instead.
func (*UpdateRolePermissionResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateRolePermissionResult) GetRolePermission() *v1.RolePermissionModel {
	if x != nil {
		return x.RolePermission
	}
	return nil
}

// delete role perm
type DeleteRolePermissionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role permission id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteRolePermissionParams) Reset() {
	*x = DeleteRolePermissionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRolePermissionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRolePermissionParams) ProtoMessage() {}

func (x *DeleteRolePermissionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRolePermissionParams.ProtoReflect.Descriptor instead.
func (*DeleteRolePermissionParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteRolePermissionParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete role permission res
type DeleteRolePermissionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteRolePermissionResult) Reset() {
	*x = DeleteRolePermissionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRolePermissionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRolePermissionResult) ProtoMessage() {}

func (x *DeleteRolePermissionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRolePermissionResult.ProtoReflect.Descriptor instead.
func (*DeleteRolePermissionResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{15}
}

// get role permissions req
type DescribeRolePermissionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	RoleId int64 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
}

func (x *DescribeRolePermissionsParams) Reset() {
	*x = DescribeRolePermissionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRolePermissionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRolePermissionsParams) ProtoMessage() {}

func (x *DescribeRolePermissionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRolePermissionsParams.ProtoReflect.Descriptor instead.
func (*DescribeRolePermissionsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{16}
}

func (x *DescribeRolePermissionsParams) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

// describe role permissions res
type DescribeRolePermissionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role permissions
	RolePermissions []*v1.RolePermissionModel `protobuf:"bytes,1,rep,name=role_permissions,json=rolePermissions,proto3" json:"role_permissions,omitempty"`
}

func (x *DescribeRolePermissionsResult) Reset() {
	*x = DescribeRolePermissionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeRolePermissionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeRolePermissionsResult) ProtoMessage() {}

func (x *DescribeRolePermissionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeRolePermissionsResult.ProtoReflect.Descriptor instead.
func (*DescribeRolePermissionsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{17}
}

func (x *DescribeRolePermissionsResult) GetRolePermissions() []*v1.RolePermissionModel {
	if x != nil {
		return x.RolePermissions
	}
	return nil
}

// DescribePermissionsParams
type DescribePermissionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// list point
	// list tree by default
	// set as true to list all type=point permissions
	ListPoint *bool `protobuf:"varint,1,opt,name=list_point,json=listPoint,proto3,oneof" json:"list_point,omitempty"`
}

func (x *DescribePermissionsParams) Reset() {
	*x = DescribePermissionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribePermissionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribePermissionsParams) ProtoMessage() {}

func (x *DescribePermissionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribePermissionsParams.ProtoReflect.Descriptor instead.
func (*DescribePermissionsParams) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{18}
}

func (x *DescribePermissionsParams) GetListPoint() bool {
	if x != nil && x.ListPoint != nil {
		return *x.ListPoint
	}
	return false
}

// DescribePermissionsResult
type DescribePermissionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// permissions tree
	Permissions []*v1.PermissionModel `protobuf:"bytes,1,rep,name=permissions,proto3" json:"permissions,omitempty"`
}

func (x *DescribePermissionsResult) Reset() {
	*x = DescribePermissionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribePermissionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribePermissionsResult) ProtoMessage() {}

func (x *DescribePermissionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribePermissionsResult.ProtoReflect.Descriptor instead.
func (*DescribePermissionsResult) Descriptor() ([]byte, []int) {
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP(), []int{19}
}

func (x *DescribePermissionsResult) GetPermissions() []*v1.PermissionModel {
	if x != nil {
		return x.Permissions
	}
	return nil
}

var File_moego_admin_admin_permission_v1_role_admin_proto protoreflect.FileDescriptor

var file_moego_admin_admin_permission_v1_role_admin_proto_rawDesc = []byte{
	0x0a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x3b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3d,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5d, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x53, 0x0a, 0x10,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x3f, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x72, 0x6f, 0x6c,
	0x65, 0x22, 0xf1, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x6f,
	0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x35, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75,
	0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x6e, 0x74, 0x36, 0x34, 0x4c, 0x69, 0x73,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x88, 0x01, 0x01,
	0x12, 0x3a, 0x0a, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x48, 0x01, 0x52, 0x05, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x02, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x88, 0x01, 0x01, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x69, 0x64, 0x73, 0x42, 0x08, 0x0a, 0x06,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9c, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x41, 0x0a,
	0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73,
	0x12, 0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69,
	0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5e, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x19, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x42, 0x11, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x22, 0x50, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3f, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x99, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x32, 0x48, 0x00, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x48, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x53, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3f, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x2b, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xba, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x0a, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x44,
	0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x7c, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x5e, 0x0a, 0x0f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x0e, 0x72, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0xb1, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f,
	0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x29, 0x0a, 0x0a, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x10, 0x64, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0x7c, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5e, 0x0a, 0x0f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e, 0x72, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x35, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1c, 0x0a, 0x1a,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x41, 0x0a, 0x1d, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x72,
	0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x22, 0x81, 0x01,
	0x0a, 0x1d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x60, 0x0a, 0x10, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c,
	0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0f, 0x72, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x4e, 0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x22,
	0x0a, 0x0a, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x6c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x88,
	0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x22, 0x70, 0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x53,
	0x0a, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x32, 0xb6, 0x0a, 0x0a, 0x0b, 0x52, 0x6f, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x72, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7b, 0x0a, 0x0d, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x69, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x12,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x72, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x31, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x72, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x90, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x99, 0x01, 0x0a, 0x17, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x90, 0x01, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x90, 0x01, 0x0a, 0x14, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8d, 0x01, 0x0a,
	0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x96, 0x01, 0x0a,
	0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x2e, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x2f, 0x61, 0x64, 0x6d,
	0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x3b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_admin_admin_permission_v1_role_admin_proto_rawDescOnce sync.Once
	file_moego_admin_admin_permission_v1_role_admin_proto_rawDescData = file_moego_admin_admin_permission_v1_role_admin_proto_rawDesc
)

func file_moego_admin_admin_permission_v1_role_admin_proto_rawDescGZIP() []byte {
	file_moego_admin_admin_permission_v1_role_admin_proto_rawDescOnce.Do(func() {
		file_moego_admin_admin_permission_v1_role_admin_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_admin_admin_permission_v1_role_admin_proto_rawDescData)
	})
	return file_moego_admin_admin_permission_v1_role_admin_proto_rawDescData
}

var file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_moego_admin_admin_permission_v1_role_admin_proto_goTypes = []interface{}{
	(*CreateRoleParams)(nil),              // 0: moego.admin.admin_permission.v1.CreateRoleParams
	(*CreateRoleResult)(nil),              // 1: moego.admin.admin_permission.v1.CreateRoleResult
	(*DescribeRolesParams)(nil),           // 2: moego.admin.admin_permission.v1.DescribeRolesParams
	(*DescribeRolesResult)(nil),           // 3: moego.admin.admin_permission.v1.DescribeRolesResult
	(*GetRoleParams)(nil),                 // 4: moego.admin.admin_permission.v1.GetRoleParams
	(*GetRoleResult)(nil),                 // 5: moego.admin.admin_permission.v1.GetRoleResult
	(*UpdateRoleParams)(nil),              // 6: moego.admin.admin_permission.v1.UpdateRoleParams
	(*UpdateRoleResult)(nil),              // 7: moego.admin.admin_permission.v1.UpdateRoleResult
	(*DeleteRoleParams)(nil),              // 8: moego.admin.admin_permission.v1.DeleteRoleParams
	(*DeleteRoleResult)(nil),              // 9: moego.admin.admin_permission.v1.DeleteRoleResult
	(*CreateRolePermissionParams)(nil),    // 10: moego.admin.admin_permission.v1.CreateRolePermissionParams
	(*CreateRolePermissionResult)(nil),    // 11: moego.admin.admin_permission.v1.CreateRolePermissionResult
	(*UpdateRolePermissionParams)(nil),    // 12: moego.admin.admin_permission.v1.UpdateRolePermissionParams
	(*UpdateRolePermissionResult)(nil),    // 13: moego.admin.admin_permission.v1.UpdateRolePermissionResult
	(*DeleteRolePermissionParams)(nil),    // 14: moego.admin.admin_permission.v1.DeleteRolePermissionParams
	(*DeleteRolePermissionResult)(nil),    // 15: moego.admin.admin_permission.v1.DeleteRolePermissionResult
	(*DescribeRolePermissionsParams)(nil), // 16: moego.admin.admin_permission.v1.DescribeRolePermissionsParams
	(*DescribeRolePermissionsResult)(nil), // 17: moego.admin.admin_permission.v1.DescribeRolePermissionsResult
	(*DescribePermissionsParams)(nil),     // 18: moego.admin.admin_permission.v1.DescribePermissionsParams
	(*DescribePermissionsResult)(nil),     // 19: moego.admin.admin_permission.v1.DescribePermissionsResult
	(*v1.RoleModel)(nil),                  // 20: moego.models.admin_permission.v1.RoleModel
	(*v11.Int64ListValue)(nil),            // 21: moego.utils.v1.Int64ListValue
	(*v11.StringListValue)(nil),           // 22: moego.utils.v1.StringListValue
	(*v2.PaginationRequest)(nil),          // 23: moego.utils.v2.PaginationRequest
	(*v2.PaginationResponse)(nil),         // 24: moego.utils.v2.PaginationResponse
	(*v1.FilterDef)(nil),                  // 25: moego.models.admin_permission.v1.FilterDef
	(*v1.RolePermissionModel)(nil),        // 26: moego.models.admin_permission.v1.RolePermissionModel
	(*v1.PermissionModel)(nil),            // 27: moego.models.admin_permission.v1.PermissionModel
}
var file_moego_admin_admin_permission_v1_role_admin_proto_depIdxs = []int32{
	20, // 0: moego.admin.admin_permission.v1.CreateRoleResult.role:type_name -> moego.models.admin_permission.v1.RoleModel
	21, // 1: moego.admin.admin_permission.v1.DescribeRolesParams.ids:type_name -> moego.utils.v1.Int64ListValue
	22, // 2: moego.admin.admin_permission.v1.DescribeRolesParams.names:type_name -> moego.utils.v1.StringListValue
	23, // 3: moego.admin.admin_permission.v1.DescribeRolesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	20, // 4: moego.admin.admin_permission.v1.DescribeRolesResult.roles:type_name -> moego.models.admin_permission.v1.RoleModel
	24, // 5: moego.admin.admin_permission.v1.DescribeRolesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	20, // 6: moego.admin.admin_permission.v1.GetRoleResult.role:type_name -> moego.models.admin_permission.v1.RoleModel
	20, // 7: moego.admin.admin_permission.v1.UpdateRoleResult.role:type_name -> moego.models.admin_permission.v1.RoleModel
	25, // 8: moego.admin.admin_permission.v1.CreateRolePermissionParams.filters:type_name -> moego.models.admin_permission.v1.FilterDef
	26, // 9: moego.admin.admin_permission.v1.CreateRolePermissionResult.role_permission:type_name -> moego.models.admin_permission.v1.RolePermissionModel
	25, // 10: moego.admin.admin_permission.v1.UpdateRolePermissionParams.filters:type_name -> moego.models.admin_permission.v1.FilterDef
	26, // 11: moego.admin.admin_permission.v1.UpdateRolePermissionResult.role_permission:type_name -> moego.models.admin_permission.v1.RolePermissionModel
	26, // 12: moego.admin.admin_permission.v1.DescribeRolePermissionsResult.role_permissions:type_name -> moego.models.admin_permission.v1.RolePermissionModel
	27, // 13: moego.admin.admin_permission.v1.DescribePermissionsResult.permissions:type_name -> moego.models.admin_permission.v1.PermissionModel
	0,  // 14: moego.admin.admin_permission.v1.RoleService.CreateRole:input_type -> moego.admin.admin_permission.v1.CreateRoleParams
	2,  // 15: moego.admin.admin_permission.v1.RoleService.DescribeRoles:input_type -> moego.admin.admin_permission.v1.DescribeRolesParams
	4,  // 16: moego.admin.admin_permission.v1.RoleService.GetRole:input_type -> moego.admin.admin_permission.v1.GetRoleParams
	6,  // 17: moego.admin.admin_permission.v1.RoleService.UpdateRole:input_type -> moego.admin.admin_permission.v1.UpdateRoleParams
	8,  // 18: moego.admin.admin_permission.v1.RoleService.DeleteRole:input_type -> moego.admin.admin_permission.v1.DeleteRoleParams
	10, // 19: moego.admin.admin_permission.v1.RoleService.CreateRolePermission:input_type -> moego.admin.admin_permission.v1.CreateRolePermissionParams
	16, // 20: moego.admin.admin_permission.v1.RoleService.DescribeRolePermissions:input_type -> moego.admin.admin_permission.v1.DescribeRolePermissionsParams
	12, // 21: moego.admin.admin_permission.v1.RoleService.UpdateRolePermission:input_type -> moego.admin.admin_permission.v1.UpdateRolePermissionParams
	14, // 22: moego.admin.admin_permission.v1.RoleService.DeleteRolePermission:input_type -> moego.admin.admin_permission.v1.DeleteRolePermissionParams
	18, // 23: moego.admin.admin_permission.v1.RoleService.DescribePermissions:input_type -> moego.admin.admin_permission.v1.DescribePermissionsParams
	1,  // 24: moego.admin.admin_permission.v1.RoleService.CreateRole:output_type -> moego.admin.admin_permission.v1.CreateRoleResult
	3,  // 25: moego.admin.admin_permission.v1.RoleService.DescribeRoles:output_type -> moego.admin.admin_permission.v1.DescribeRolesResult
	5,  // 26: moego.admin.admin_permission.v1.RoleService.GetRole:output_type -> moego.admin.admin_permission.v1.GetRoleResult
	7,  // 27: moego.admin.admin_permission.v1.RoleService.UpdateRole:output_type -> moego.admin.admin_permission.v1.UpdateRoleResult
	9,  // 28: moego.admin.admin_permission.v1.RoleService.DeleteRole:output_type -> moego.admin.admin_permission.v1.DeleteRoleResult
	11, // 29: moego.admin.admin_permission.v1.RoleService.CreateRolePermission:output_type -> moego.admin.admin_permission.v1.CreateRolePermissionResult
	17, // 30: moego.admin.admin_permission.v1.RoleService.DescribeRolePermissions:output_type -> moego.admin.admin_permission.v1.DescribeRolePermissionsResult
	13, // 31: moego.admin.admin_permission.v1.RoleService.UpdateRolePermission:output_type -> moego.admin.admin_permission.v1.UpdateRolePermissionResult
	15, // 32: moego.admin.admin_permission.v1.RoleService.DeleteRolePermission:output_type -> moego.admin.admin_permission.v1.DeleteRolePermissionResult
	19, // 33: moego.admin.admin_permission.v1.RoleService.DescribePermissions:output_type -> moego.admin.admin_permission.v1.DescribePermissionsResult
	24, // [24:34] is the sub-list for method output_type
	14, // [14:24] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_moego_admin_admin_permission_v1_role_admin_proto_init() }
func file_moego_admin_admin_permission_v1_role_admin_proto_init() {
	if File_moego_admin_admin_permission_v1_role_admin_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRolesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRolesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRolePermissionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRolePermissionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRolePermissionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRolePermissionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRolePermissionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRolePermissionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRolePermissionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeRolePermissionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribePermissionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribePermissionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*GetRoleParams_Id)(nil),
		(*GetRoleParams_Name)(nil),
	}
	file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes[18].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_admin_admin_permission_v1_role_admin_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_admin_admin_permission_v1_role_admin_proto_goTypes,
		DependencyIndexes: file_moego_admin_admin_permission_v1_role_admin_proto_depIdxs,
		MessageInfos:      file_moego_admin_admin_permission_v1_role_admin_proto_msgTypes,
	}.Build()
	File_moego_admin_admin_permission_v1_role_admin_proto = out.File
	file_moego_admin_admin_permission_v1_role_admin_proto_rawDesc = nil
	file_moego_admin_admin_permission_v1_role_admin_proto_goTypes = nil
	file_moego_admin_admin_permission_v1_role_admin_proto_depIdxs = nil
}
