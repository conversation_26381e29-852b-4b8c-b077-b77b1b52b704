package com.moego.svc.organization.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.organization.entity.MoeVanStaff;
import com.moego.svc.organization.mapper.base.BaseMoeVanStaffMapper;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MoeVanStaffMapper extends BaseMoeVanStaffMapper, DynamicDataSource<MoeVanStaffMapper> {
    List<MoeVanStaff> selectByStaffIdsList(@Param("staffIds") List<Long> staffIds);
}
