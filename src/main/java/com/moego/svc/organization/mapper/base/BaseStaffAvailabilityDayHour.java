package com.moego.svc.organization.mapper.base;

import com.moego.svc.organization.entity.StaffAvailabilityDayHour;
import com.moego.svc.organization.entity.StaffAvailabilityDayHourExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseStaffAvailabilityDayHour {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    long countByExample(StaffAvailabilityDayHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int deleteByExample(StaffAvailabilityDayHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int insert(StaffAvailabilityDayHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int insertSelective(StaffAvailabilityDayHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    List<StaffAvailabilityDayHour> selectByExampleWithBLOBs(StaffAvailabilityDayHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    List<StaffAvailabilityDayHour> selectByExample(StaffAvailabilityDayHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    StaffAvailabilityDayHour selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int updateByExampleSelective(
            @Param("row") StaffAvailabilityDayHour row, @Param("example") StaffAvailabilityDayHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(
            @Param("row") StaffAvailabilityDayHour row, @Param("example") StaffAvailabilityDayHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int updateByExample(
            @Param("row") StaffAvailabilityDayHour row, @Param("example") StaffAvailabilityDayHourExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(StaffAvailabilityDayHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(StaffAvailabilityDayHour row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table staff_availability_day_hour
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(StaffAvailabilityDayHour row);
}
