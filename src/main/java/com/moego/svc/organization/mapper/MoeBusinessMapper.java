package com.moego.svc.organization.mapper;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.organization.dto.BusinessCidDTO;
import com.moego.svc.organization.mapper.base.BaseMoeBusinessMapper;
import java.util.List;

public interface MoeBusinessMapper extends BaseMoeBusinessMapper, DynamicDataSource<MoeBusinessMapper> {

    List<Integer> selectAllBusinessIdsByCompanyId(Long companyId);

    List<BusinessCidDTO> selectAllBusinessCid();
}
