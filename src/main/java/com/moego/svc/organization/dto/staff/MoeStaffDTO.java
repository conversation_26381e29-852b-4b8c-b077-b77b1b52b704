package com.moego.svc.organization.dto.staff;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class MoeStaffDTO {

    private Integer id;
    private Integer companyId;
    private Integer businessId;
    private Integer accountId;
    private Integer roleId;
    private String avatarPath;
    private String firstName;
    private String lastName;
    private Byte employeeCategory;
    private String phoneNumber;
    private Long hireDate;
    private Long fireDate;
    private Byte allowLogin;
    private Integer groupLeaderId;
    private String note;
    private Byte inactive;
    private Byte status;
    private Integer createById;
    private Integer sort;
    private Byte bookOnlineAvailable;
    private Long createTime;
    private Long updateTime;
    private Byte showOnCalendar;
    private Byte showCalendarStaffAll;
    private String accessCode;
    private String token;
    private String inviteCode;
    // commission相关
    private Byte payBy;
    private Integer servicePayRate;
    private Integer addonPayRate;
    private BigDecimal hourlyPayRate;
    private Integer tipsPayRate;
    private Boolean isWorkingStaff;
    private Boolean isAllShownOnCalendar;
}
