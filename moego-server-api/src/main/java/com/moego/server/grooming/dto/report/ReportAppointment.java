package com.moego.server.grooming.dto.report;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReportAppointment {

    private Integer apptId;
    private Integer customerId;
    private String orderId;
    private String customerName;
    private String apptDate;
    private Integer startTime;
    private Integer endTime;
    private BigDecimal unpaidAmount;
    private Boolean noShow;
    private Integer status;
    private Integer cancelByType;
    // staff name if it is cancelled by business, otherwise null
    private String cancelByName;
    private Boolean noShowCharged;
}
