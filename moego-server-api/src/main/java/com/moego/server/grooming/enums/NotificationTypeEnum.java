package com.moego.server.grooming.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum NotificationTypeEnum {
    PICKUP_REMINDER("Ready for pickup message"),
    REVIEW_BOOSTER("Review booster"),
    GROOMING_REPORT("Grooming report"),
    DAILY_REPORT("Daily report");

    private final String value;

    public static NotificationTypeEnum fromString(String value) {
        for (NotificationTypeEnum notificationTypeEnum : NotificationTypeEnum.values()) {
            if (notificationTypeEnum.getValue().equals(value)) {
                return notificationTypeEnum;
            }
        }
        return null;
    }

    @JsonValue
    public String getValue() {
        return value;
    }
}
