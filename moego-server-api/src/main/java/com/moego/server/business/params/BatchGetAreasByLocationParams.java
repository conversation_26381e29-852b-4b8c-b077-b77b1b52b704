package com.moego.server.business.params;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchGetAreasByLocationParams {
    private Long businessId;
    private Long companyId;
    // 限定在若干 service area，为 null 时，表示不做限制
    private List<Integer> areaIds;
    private List<GetAreasByLocationParams> locations;

    public BatchGetAreasByLocationParams(
            Long businessId, List<Integer> areaIds, List<GetAreasByLocationParams> locations) {
        this.businessId = businessId;
        this.areaIds = areaIds;
        this.locations = locations;
    }
}
