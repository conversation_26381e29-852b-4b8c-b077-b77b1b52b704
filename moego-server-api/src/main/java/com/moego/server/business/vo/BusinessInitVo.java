package com.moego.server.business.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.moego.server.customer.params.CustomerInitDataParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessInitVo {

    @Schema(description = "公司主键，company owner在该公司下添加新的business, 新建公司时，该字段设置为null")
    private Integer companyId;

    @NotNull
    private String businessName;

    private String phoneNumber;
    private String website;

    @NotNull
    private Byte source;

    private String knowAboutUs;

    @NotNull
    private String country;

    @Schema(description = "Country code, ISO 3166-1 alpha-2 code")
    private String countryAlpha2Code;

    @NotNull
    @Schema(description = "0-Mobile Grooming  1-Grooming Salon")
    private Byte appType;

    private Integer howManyLocations;
    private Integer howManyStaff;

    /**
     * this field is not used anymore
     */
    @Deprecated
    private CustomerInitDataParam petTypeInfo;

    @NotNull
    private String timezoneName;

    @NotNull
    private Integer timezoneSeconds;

    @NotNull
    private String currencySymbol;

    @NotNull
    private String currencyCode;

    @NotNull
    private Byte dateFormatType;

    // address
    private String address1;
    private String address2;
    private String addressCity;
    private String addressState;
    private String addressZipcode;
    private String addressCountry;
    private String addressLat;
    private String addressLng;

    // 注册来源，0:web，1:android,2:ios
    private Integer sourceFrom;

    @Schema(
            description =
                    """
                选项1）Less than 7
                选项2）7 to 20
                选项3）21 to 50
                选项4）More than 50""")
    private Byte apptPerWeek;

    @Schema(
            description =
                    """
            选项1）Less than 6 months
            选项2）6 months to 2 years
            选项3）2 years to 1`0 years
            选项4）More than 10 years""")
    private Byte businessYears;

    @Schema(
            description =
                    """
            选项1）Just start my business
            选项2）Move from paper
            选项3）Transfer from other software""")
    private Byte moveFrom;

    @Schema(description = "是否显示retail模块，0-关闭，1-打开")
    private Byte retailEnable;

    @Schema(description = "是否打开payment method 中的credit card")
    private Boolean enableCreditCard;
}
