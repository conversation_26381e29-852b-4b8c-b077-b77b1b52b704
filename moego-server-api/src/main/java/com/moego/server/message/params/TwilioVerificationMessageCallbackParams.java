package com.moego.server.message.params;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2020-07-05 11:49
 */
@Data
@ToString
public class TwilioVerificationMessageCallbackParams {

    public static final int SMS_STATUS_UNKNOWN = 0; // 状态未知
    public static final int SMS_STATUS_SENDING = 1; // 发送中
    public static final int SMS_STATUS_DELIVERED = 2; // 发送成功
    public static final int SMS_STATUS_FAILED = 3; // 发送失败

    private String phoneNumber;
    private String smsSid;
    private String smsStatus;
    private String messageStatus;
    private String to;
    private String messageSid;
    private String accountSid;
    private String from;
    private String apiVersion;
    private Integer errorCode;
    private String errorMessage;

    public Integer smsSendStatus() {
        if ("sent".equals(smsStatus) || "queued".equals(smsStatus)) {
            return SMS_STATUS_SENDING;
        }
        if ("delivered".equals(smsStatus)) {
            return SMS_STATUS_DELIVERED;
        }
        if ("undelivered".equals(smsStatus) || "failed".equals(smsStatus)) {
            return SMS_STATUS_FAILED;
        }

        return SMS_STATUS_UNKNOWN;
    }

    public static boolean isSendSuccessed(int smsSendStatus) {
        return TwilioVerificationMessageCallbackParams.SMS_STATUS_DELIVERED == smsSendStatus;
    }

    public static boolean isSendFailed(int smsSendStatus) {
        return TwilioVerificationMessageCallbackParams.SMS_STATUS_FAILED == smsSendStatus;
    }

    public Boolean isRead() {
        return "read".equalsIgnoreCase(messageStatus);
    }

    public Boolean isDelete() {
        return "delete".equalsIgnoreCase(messageStatus);
    }
}
